# Generated by Django 5.2.3 on 2025-07-16 10:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0002_esyndic_glocative"),
    ]

    operations = [
        migrations.CreateModel(
            name="<PERSON><PERSON>",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("data", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
