# Add these imports at the top
from .models import ProjectAnalytics, StockAnalytics, BusinessKPIs, AlertSystem
from rest_framework import serializers

# ========================================================
# Analytics Serializers
# ========================================================


class ProjectAnalyticsSerializer(serializers.ModelSerializer):
    """
    Serializer for Project Analytics data
    """

    budget_variance_display = serializers.SerializerMethodField()
    performance_status = serializers.SerializerMethodField()
    profitability_status = serializers.SerializerMethodField()

    class Meta:
        model = ProjectAnalytics
        fields = [
            "id",
            "project_name",
            "project_id",
            "estimated_budget",
            "actual_cost",
            "budget_variance",
            "budget_variance_percentage",
            "budget_variance_display",
            "planned_duration",
            "actual_duration",
            "completion_percentage",
            "stock_utilization_rate",
            "waste_percentage",
            "stock_cost_efficiency",
            "profitability_index",
            "delivery_performance",
            "performance_status",
            "profitability_status",
            "analysis_date",
            "data_sources",
        ]
        read_only_fields = ["id", "analysis_date"]

    def get_budget_variance_display(self, obj):
        if obj.budget_variance_percentage:
            status = (
                "Over Budget" if obj.budget_variance_percentage > 0 else "Under Budget"
            )
            return f"{status} ({obj.budget_variance_percentage:.1f}%)"
        return "N/A"

    def get_performance_status(self, obj):
        if obj.completion_percentage is None:
            return "Unknown"
        elif obj.completion_percentage >= 90:
            return "Excellent"
        elif obj.completion_percentage >= 75:
            return "Good"
        elif obj.completion_percentage >= 50:
            return "Average"
        else:
            return "Poor"

    def get_profitability_status(self, obj):
        if obj.budget_variance_percentage is None:
            return "Unknown"
        elif obj.budget_variance_percentage <= -5:
            return "Highly Profitable"
        elif obj.budget_variance_percentage <= 0:
            return "Profitable"
        elif obj.budget_variance_percentage <= 10:
            return "Acceptable"
        else:
            return "Concerning"


class StockAnalyticsSerializer(serializers.ModelSerializer):
    """
    Serializer for Stock Analytics data
    """

    efficiency_rating = serializers.SerializerMethodField()
    period_display = serializers.SerializerMethodField()

    class Meta:
        model = StockAnalytics
        fields = [
            "id",
            "analysis_period",
            "period_start",
            "period_end",
            "period_display",
            "total_stock_value",
            "stock_turnover_ratio",
            "average_stock_level",
            "stockout_incidents",
            "carrying_cost_percentage",
            "demand_forecast_accuracy",
            "supplier_performance",
            "materials_performance",
            "efficiency_rating",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_efficiency_rating(self, obj):
        # Calculate overall efficiency based on multiple factors
        score = 0
        factors = 0

        if obj.stock_turnover_ratio:
            score += min(obj.stock_turnover_ratio * 10, 100)
            factors += 1

        if obj.demand_forecast_accuracy:
            score += obj.demand_forecast_accuracy
            factors += 1

        if obj.supplier_performance:
            score += obj.supplier_performance
            factors += 1

        if factors > 0:
            avg_score = score / factors
            if avg_score >= 85:
                return "Excellent"
            elif avg_score >= 70:
                return "Good"
            elif avg_score >= 55:
                return "Average"
            else:
                return "Needs Improvement"

        return "Insufficient Data"

    def get_period_display(self, obj):
        return f"{obj.analysis_period.title()} ({obj.period_start} to {obj.period_end})"


class BusinessKPIsSerializer(serializers.ModelSerializer):
    """
    Serializer for Business KPIs
    """

    performance_trend = serializers.SerializerMethodField()
    kpi_summary = serializers.SerializerMethodField()

    class Meta:
        model = BusinessKPIs
        fields = [
            "id",
            "kpi_date",
            "kpi_type",
            "total_revenue",
            "gross_profit_margin",
            "operating_margin",
            "roi_percentage",
            "project_completion_rate",
            "average_project_delay",
            "customer_satisfaction_score",
            "market_share_percentage",
            "sales_conversion_rate",
            "budget_overrun_frequency",
            "quality_incident_rate",
            "performance_trend",
            "kpi_summary",
            "kpi_data",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_performance_trend(self, obj):
        # Compare with previous period
        previous_kpi = (
            BusinessKPIs.objects.filter(
                kpi_type=obj.kpi_type, kpi_date__lt=obj.kpi_date
            )
            .order_by("-kpi_date")
            .first()
        )

        if not previous_kpi:
            return "No Previous Data"

        # Compare key metrics
        trends = {}

        if obj.project_completion_rate and previous_kpi.project_completion_rate:
            change = obj.project_completion_rate - previous_kpi.project_completion_rate
            trends["completion_rate"] = (
                "↑" if change > 0 else "↓" if change < 0 else "→"
            )

        if obj.gross_profit_margin and previous_kpi.gross_profit_margin:
            change = obj.gross_profit_margin - previous_kpi.gross_profit_margin
            trends["profit_margin"] = "↑" if change > 0 else "↓" if change < 0 else "→"

        if obj.roi_percentage and previous_kpi.roi_percentage:
            change = obj.roi_percentage - previous_kpi.roi_percentage
            trends["roi"] = "↑" if change > 0 else "↓" if change < 0 else "→"

        return trends

    def get_kpi_summary(self, obj):
        summary = {
            "overall_health": "Good",
            "key_strengths": [],
            "areas_for_improvement": [],
            "critical_issues": [],
        }

        # Analyze completion rate
        if obj.project_completion_rate:
            if obj.project_completion_rate >= 90:
                summary["key_strengths"].append("Excellent project completion rate")
            elif obj.project_completion_rate < 70:
                summary["areas_for_improvement"].append("Low project completion rate")

        # Analyze profitability
        if obj.gross_profit_margin:
            if obj.gross_profit_margin >= 20:
                summary["key_strengths"].append("Strong profit margins")
            elif obj.gross_profit_margin < 10:
                summary["critical_issues"].append("Low profit margins")

        # Analyze budget overruns
        if obj.budget_overrun_frequency:
            if obj.budget_overrun_frequency > 30:
                summary["critical_issues"].append("High budget overrun frequency")
            elif obj.budget_overrun_frequency > 15:
                summary["areas_for_improvement"].append("Moderate budget overruns")

        # Determine overall health
        if summary["critical_issues"]:
            summary["overall_health"] = "Critical"
        elif summary["areas_for_improvement"] and not summary["key_strengths"]:
            summary["overall_health"] = "Needs Attention"
        elif summary["key_strengths"]:
            summary["overall_health"] = "Good"

        return summary


class AlertSystemSerializer(serializers.ModelSerializer):
    """
    Serializer for Alert System
    """

    alert_type_display = serializers.CharField(
        source="get_alert_type_display", read_only=True
    )
    severity_display = serializers.CharField(
        source="get_severity_display", read_only=True
    )
    time_since_created = serializers.SerializerMethodField()
    resolution_time = serializers.SerializerMethodField()

    class Meta:
        model = AlertSystem
        fields = [
            "id",
            "alert_type",
            "alert_type_display",
            "severity",
            "severity_display",
            "title",
            "message",
            "project_name",
            "affected_area",
            "threshold_value",
            "actual_value",
            "variance_percentage",
            "is_resolved",
            "resolved_at",
            "time_since_created",
            "resolution_time",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_time_since_created(self, obj):
        from django.utils.timesince import timesince

        return timesince(obj.created_at)

    def get_resolution_time(self, obj):
        if obj.is_resolved and obj.resolved_at:
            delta = obj.resolved_at - obj.created_at
            hours = delta.total_seconds() / 3600
            if hours < 24:
                return f"{hours:.1f} hours"
            else:
                days = hours / 24
                return f"{days:.1f} days"
        return None


# Dashboard Summary Serializers
# ========================================================


class DashboardSummarySerializer(serializers.Serializer):
    """
    Serializer for dashboard summary data
    """

    # Overview metrics
    total_projects = serializers.IntegerField()
    active_projects = serializers.IntegerField()
    completed_projects = serializers.IntegerField()
    projects_at_risk = serializers.IntegerField()

    # Financial summary
    total_budget = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_spent = serializers.DecimalField(max_digits=15, decimal_places=2)
    average_budget_variance = serializers.FloatField()
    total_savings = serializers.DecimalField(max_digits=15, decimal_places=2)

    # Performance metrics
    overall_completion_rate = serializers.FloatField()
    on_time_delivery_rate = serializers.FloatField()
    average_project_duration = serializers.FloatField()

    # Stock metrics
    total_stock_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    stock_efficiency_score = serializers.FloatField()
    active_alerts_count = serializers.IntegerField()

    # Trends
    performance_trend = serializers.CharField()
    budget_trend = serializers.CharField()

    # Last updated
    last_analysis_date = serializers.DateTimeField()


class AnalyticsInsightSerializer(serializers.Serializer):
    """
    Serializer for analytics insights and recommendations
    """

    insight_type = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField()
    impact_level = serializers.CharField()  # high, medium, low
    recommendation = serializers.CharField()
    supporting_data = serializers.JSONField()
    confidence_score = serializers.FloatField()
    created_at = serializers.DateTimeField()
