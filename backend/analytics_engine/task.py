# tasks.py
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging
from .analytic_engine import AnalyticsEngine
from .models import BusinessKPIs, AlertSystem

logger = logging.getLogger(__name__)


@shared_task
def run_daily_analytics():
    """
    Run daily analytics analysis
    """
    try:
        logger.info("Starting daily analytics analysis...")

        engine = AnalyticsEngine()
        results = engine.run_complete_analysis()

        logger.info(
            f"Daily analytics completed successfully. "
            f"Projects analyzed: {len(results.get('project_analytics', []))}, "
            f"Alerts generated: {len(results.get('alerts_generated', []))}"
        )

        return {
            "status": "success",
            "projects_analyzed": len(results.get("project_analytics", [])),
            "alerts_generated": len(results.get("alerts_generated", [])),
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Daily analytics analysis failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat(),
        }


@shared_task
def run_weekly_analytics():
    """
    Run comprehensive weekly analytics
    """
    try:
        logger.info("Starting weekly analytics analysis...")

        engine = AnalyticsEngine()

        # Run complete analysis
        results = engine.run_complete_analysis()

        # Generate weekly insights
        weekly_insights = engine.generate_weekly_insights()

        # Send weekly reports (implement email sending here)
        # send_weekly_reports(results, weekly_insights)

        logger.info("Weekly analytics analysis completed successfully")

        return {
            "status": "success",
            "analysis_results": results,
            "insights": weekly_insights,
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Weekly analytics analysis failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat(),
        }


@shared_task
def cleanup_old_alerts():
    """
    Clean up resolved alerts older than 30 days
    """
    try:
        thirty_days_ago = timezone.now() - timedelta(days=30)

        old_alerts = AlertSystem.objects.filter(
            is_resolved=True, resolved_at__lt=thirty_days_ago
        )

        count = old_alerts.count()
        old_alerts.delete()

        logger.info(f"Cleaned up {count} old resolved alerts")

        return {
            "status": "success",
            "alerts_cleaned": count,
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Alert cleanup failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat(),
        }


@shared_task
def generate_monthly_reports():
    """
    Generate monthly business reports
    """
    try:
        logger.info("Starting monthly report generation...")

        # Calculate monthly KPIs
        today = timezone.now().date()
        first_day_of_month = today.replace(day=1)

        engine = AnalyticsEngine()
        monthly_kpis = engine.calculate_monthly_kpis(first_day_of_month)

        # Save monthly KPIs
        BusinessKPIs.objects.update_or_create(
            kpi_date=first_day_of_month, kpi_type="monthly", defaults=monthly_kpis
        )

        # Generate and send monthly reports
        # generate_monthly_report_pdf(monthly_kpis)
        # send_monthly_reports(monthly_kpis)

        logger.info("Monthly report generation completed successfully")

        return {
            "status": "success",
            "report_period": first_day_of_month.isoformat(),
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Monthly report generation failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat(),
        }


@shared_task
def monitor_system_health():
    """
    Monitor the health of the analytics system
    """
    try:
        health_status = {
            "data_sources": {},
            "analytics_performance": {},
            "alert_system": {},
            "overall_health": "healthy",
        }

        # Check data freshness
        from .models import DQEData, GStockApprovisionnement, GProjet

        one_day_ago = timezone.now() - timedelta(days=1)

        # Check if we have recent data from each source
        health_status["data_sources"] = {
            "dqe": DQEData.objects.filter(created_at__gte=one_day_ago).exists(),
            "gstock": GStockApprovisionnement.objects.filter(
                created_at__gte=one_day_ago
            ).exists(),
            "gprojet": GProjet.objects.filter(created_at__gte=one_day_ago).exists(),
        }

        # Check analytics performance
        recent_analytics = engine.get_recent_analytics_performance()
        health_status["analytics_performance"] = recent_analytics

        # Check alert system
        active_critical_alerts = AlertSystem.objects.filter(
            is_resolved=False, severity="critical"
        ).count()

        health_status["alert_system"] = {
            "critical_alerts": active_critical_alerts,
            "status": "warning" if active_critical_alerts > 0 else "healthy",
        }

        # Determine overall health
        if active_critical_alerts > 5:
            health_status["overall_health"] = "critical"
        elif active_critical_alerts > 0 or not all(
            health_status["data_sources"].values()
        ):
            health_status["overall_health"] = "warning"

        logger.info(
            f"System health check completed. Status: {health_status['overall_health']}"
        )

        return {
            "status": "success",
            "health_status": health_status,
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"System health monitoring failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat(),
        }
