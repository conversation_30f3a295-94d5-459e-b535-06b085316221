# urls.py - Add these new routes
from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    AnalyticsViewSet,
    ProjectAnalyticsViewSet,
    StockAnalyticsViewSet,
    BusinessKPIsViewSet,
    AlertSystemViewSet,
)

router = DefaultRouter()

router.register(r"analytics", AnalyticsViewSet, basename="analytics")
router.register(
    r"project-analytics", ProjectAnalyticsViewSet, basename="project-analytics"
)
router.register(r"stock-analytics", StockAnalyticsViewSet, basename="stock-analytics")
router.register(r"business-kpis", BusinessKPIsViewSet, basename="business-kpis")
router.register(r"alerts", AlertSystemViewSet, basename="alerts")

urlpatterns = [
    path("", include(router.urls)),
]
