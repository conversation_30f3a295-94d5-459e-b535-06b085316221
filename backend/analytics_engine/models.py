from django.db import models

# ========================================================
# Analytics Engine Models
# ========================================================


class ProjectAnalytics(models.Model):
    """
    Aggregated analytics for projects combining data from multiple sources
    """

    project_name = models.CharField(max_length=255)
    project_id = models.CharField(max_length=100, null=True, blank=True)

    # Financial Metrics
    estimated_budget = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    actual_cost = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    budget_variance = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    budget_variance_percentage = models.FloatField(null=True, blank=True)

    # Timeline Metrics
    planned_duration = models.IntegerField(null=True, blank=True)  # in days
    actual_duration = models.IntegerField(null=True, blank=True)
    completion_percentage = models.FloatField(null=True, blank=True)

    # Stock Efficiency Metrics
    stock_utilization_rate = models.FloatField(null=True, blank=True)
    waste_percentage = models.FloatField(null=True, blank=True)
    stock_cost_efficiency = models.FloatField(null=True, blank=True)

    # Performance Indicators
    profitability_index = models.FloatField(null=True, blank=True)
    delivery_performance = models.CharField(
        max_length=20, null=True, blank=True
    )  # On-time, Delayed, Early

    # Meta Information
    analysis_date = models.DateTimeField(auto_now_add=True)
    data_sources = models.JSONField(default=list)  # Track which data sources were used

    class Meta:
        ordering = ["-analysis_date"]

    def __str__(self):
        return f"Analytics for {self.project_name} - {self.analysis_date.strftime('%Y-%m-%d')}"


class StockAnalytics(models.Model):
    """
    Stock performance analytics across all projects
    """

    analysis_period = models.CharField(max_length=20)  # monthly, quarterly, yearly
    period_start = models.DateField()
    period_end = models.DateField()

    # Stock Metrics
    total_stock_value = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    stock_turnover_ratio = models.FloatField(null=True, blank=True)
    average_stock_level = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    stockout_incidents = models.IntegerField(default=0)

    # Efficiency Metrics
    carrying_cost_percentage = models.FloatField(null=True, blank=True)
    demand_forecast_accuracy = models.FloatField(null=True, blank=True)
    supplier_performance = models.FloatField(null=True, blank=True)

    # Material Categories Performance
    materials_performance = models.JSONField(default=dict)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Stock Analytics {self.analysis_period} ({self.period_start} - {self.period_end})"


class BusinessKPIs(models.Model):
    """
    Overall business KPIs for KAYDAN real estate operations
    """

    kpi_date = models.DateField()
    kpi_type = models.CharField(max_length=50)  # daily, weekly, monthly, quarterly

    # Financial KPIs
    total_revenue = models.DecimalField(
        max_digits=15, decimal_places=2, null=True, blank=True
    )
    gross_profit_margin = models.FloatField(null=True, blank=True)
    operating_margin = models.FloatField(null=True, blank=True)
    roi_percentage = models.FloatField(null=True, blank=True)

    # Operational KPIs
    project_completion_rate = models.FloatField(null=True, blank=True)
    average_project_delay = models.FloatField(null=True, blank=True)  # in days
    customer_satisfaction_score = models.FloatField(null=True, blank=True)

    # Market KPIs
    market_share_percentage = models.FloatField(null=True, blank=True)
    sales_conversion_rate = models.FloatField(null=True, blank=True)

    # Risk Indicators
    budget_overrun_frequency = models.FloatField(null=True, blank=True)
    quality_incident_rate = models.FloatField(null=True, blank=True)

    # Raw data for detailed analysis
    kpi_data = models.JSONField(default=dict)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-kpi_date"]
        unique_together = ["kpi_date", "kpi_type"]

    def __str__(self):
        return f"KPIs {self.kpi_type} - {self.kpi_date}"


class AlertSystem(models.Model):
    """
    Automated alerts based on analytics
    """

    ALERT_TYPES = [
        ("budget_overrun", "Budget Overrun"),
        ("schedule_delay", "Schedule Delay"),
        ("stock_shortage", "Stock Shortage"),
        ("quality_issue", "Quality Issue"),
        ("performance_drop", "Performance Drop"),
        ("cost_spike", "Cost Spike"),
    ]

    SEVERITY_LEVELS = [
        ("low", "Low"),
        ("medium", "Medium"),
        ("high", "High"),
        ("critical", "Critical"),
    ]

    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)
    title = models.CharField(max_length=255)
    message = models.TextField()

    # Related objects
    project_name = models.CharField(max_length=255, null=True, blank=True)
    affected_area = models.CharField(max_length=100, null=True, blank=True)

    # Alert data
    threshold_value = models.FloatField(null=True, blank=True)
    actual_value = models.FloatField(null=True, blank=True)
    variance_percentage = models.FloatField(null=True, blank=True)

    # Status
    is_resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.get_severity_display()} {self.get_alert_type_display()}: {self.title}"
