# analytics_engine.py
import json
import pandas as pd
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Q, Avg, Sum, Count
from django.utils import timezone
from api.models import (
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
)
from .models import (
    ProjectAnalytics,
    StockAnalytics,
    BusinessKPIs,
    AlertSystem,
)


class AnalyticsEngine:
    """
    Core analytics engine for processing KAYDAN real estate data
    """

    def __init__(self):
        self.alert_thresholds = {
            "budget_variance": 10.0,  # 10% variance threshold
            "schedule_delay": 7,  # 7 days delay threshold
            "stock_shortage": 20.0,  # 20% stock level threshold
        }

    def run_complete_analysis(self):
        """
        Run complete analytics pipeline
        """
        results = {
            "project_analytics": self.analyze_projects(),
            "stock_analytics": self.analyze_stock_performance(),
            "business_kpis": self.calculate_business_kpis(),
            "alerts_generated": self.generate_alerts(),
            "analysis_timestamp": timezone.now(),
        }
        return results

    def analyze_projects(self):
        """
        Analyze project performance combining DQE, G-Projet, and G-Stock data
        """
        project_analytics = []

        # Get latest data from each source
        latest_dqe = DQEData.objects.order_by("-created_at").first()
        latest_gprojet = GProjet.objects.order_by("-created_at").first()

        if latest_dqe and latest_gprojet:
            # Process DQE data for financial metrics
            dqe_data = latest_dqe.data
            gprojet_data = latest_gprojet.data

            # Extract projects from data
            projects = self._extract_projects_data(dqe_data, gprojet_data)

            for project in projects:
                analytics = self._calculate_project_metrics(project)

                # Save to database
                project_analytics_obj, created = (
                    ProjectAnalytics.objects.update_or_create(
                        project_name=analytics["project_name"], defaults=analytics
                    )
                )

                project_analytics.append(analytics)

        return project_analytics

    def analyze_stock_performance(self):
        """
        Analyze stock management efficiency
        """
        # Get latest stock data
        latest_appro = GStockApprovisionnement.objects.order_by("-created_at").first()
        latest_sortie = GStockSortie.objects.order_by("-created_at").first()
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()
        latest_achat = GStockAchat.objects.order_by("-created_at").first()

        if all([latest_appro, latest_sortie, latest_conso, latest_achat]):
            stock_metrics = self._calculate_stock_metrics(
                latest_appro.data,
                latest_sortie.data,
                latest_conso.data,
                latest_achat.data,
            )

            # Save to database
            period_start = timezone.now().date().replace(day=1)
            period_end = timezone.now().date()

            stock_analytics, created = StockAnalytics.objects.update_or_create(
                analysis_period="monthly",
                period_start=period_start,
                period_end=period_end,
                defaults=stock_metrics,
            )

            return stock_metrics

        return {}

    def calculate_business_kpis(self):
        """
        Calculate high-level business KPIs
        """
        today = timezone.now().date()

        # Aggregate data from recent analytics
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=today - timedelta(days=30)
        )

        recent_stock = StockAnalytics.objects.filter(
            period_end__gte=today - timedelta(days=30)
        ).first()

        kpis = {
            "project_completion_rate": self._calculate_completion_rate(recent_projects),
            "average_budget_variance": self._calculate_avg_budget_variance(
                recent_projects
            ),
            "stock_efficiency_score": self._calculate_stock_efficiency(recent_stock),
            "overall_performance_score": 0.0,  # Calculated below
        }

        # Calculate overall performance score
        kpis["overall_performance_score"] = (
            kpis["project_completion_rate"] * 0.4
            + (100 - abs(kpis["average_budget_variance"])) * 0.3
            + kpis["stock_efficiency_score"] * 0.3
        )

        # Save to database
        business_kpi, created = BusinessKPIs.objects.update_or_create(
            kpi_date=today,
            kpi_type="daily",
            defaults={
                "project_completion_rate": kpis["project_completion_rate"],
                "average_project_delay": kpis["average_budget_variance"],
                "kpi_data": kpis,
            },
        )

        return kpis

    def generate_alerts(self):
        """
        Generate automated alerts based on thresholds
        """
        alerts_created = []

        # Check for budget overruns
        budget_alerts = self._check_budget_alerts()
        alerts_created.extend(budget_alerts)

        # Check for stock shortages
        stock_alerts = self._check_stock_alerts()
        alerts_created.extend(stock_alerts)

        # Check for schedule delays
        schedule_alerts = self._check_schedule_alerts()
        alerts_created.extend(schedule_alerts)

        return alerts_created

    # Helper methods
    def _extract_projects_data(self, dqe_data, gprojet_data):
        """Extract and combine project data from different sources"""
        projects = []

        # This would depend on the actual structure of your API data
        # You'll need to adapt this based on your real data format

        # Example structure - adapt to your actual data
        if isinstance(dqe_data, dict) and "projects" in dqe_data:
            for project in dqe_data["projects"]:
                project_info = {
                    "name": project.get("name", "Unknown Project"),
                    "estimated_budget": project.get("estimated_cost", 0),
                    "dqe_data": project,
                }

                # Find corresponding G-Projet data
                gprojet_info = self._find_project_in_gprojet(
                    project_info["name"], gprojet_data
                )
                if gprojet_info:
                    project_info["gprojet_data"] = gprojet_info

                projects.append(project_info)

        return projects

    def _calculate_project_metrics(self, project_data):
        """Calculate analytics metrics for a single project"""
        metrics = {
            "project_name": project_data["name"],
            "estimated_budget": Decimal(str(project_data.get("estimated_budget", 0))),
            "data_sources": ["DQE", "G-Projet"],
        }

        # Calculate budget variance if actual cost is available
        if "actual_cost" in project_data:
            actual_cost = Decimal(str(project_data["actual_cost"]))
            metrics["actual_cost"] = actual_cost
            metrics["budget_variance"] = actual_cost - metrics["estimated_budget"]

            if metrics["estimated_budget"] > 0:
                metrics["budget_variance_percentage"] = float(
                    (metrics["budget_variance"] / metrics["estimated_budget"]) * 100
                )

        # Calculate completion percentage from G-Projet data
        if "gprojet_data" in project_data:
            gprojet_info = project_data["gprojet_data"]
            metrics["completion_percentage"] = gprojet_info.get("completion_rate", 0.0)

            # Determine delivery performance
            if metrics["completion_percentage"] >= 100:
                metrics["delivery_performance"] = "Complete"
            elif metrics["completion_percentage"] >= 75:
                metrics["delivery_performance"] = "On-track"
            else:
                metrics["delivery_performance"] = "At-risk"

        return metrics

    def _calculate_stock_metrics(self, appro_data, sortie_data, conso_data, achat_data):
        """Calculate stock performance metrics"""
        metrics = {
            "total_stock_value": Decimal("0"),
            "stock_turnover_ratio": 0.0,
            "stockout_incidents": 0,
            "materials_performance": {},
        }

        # Process stock data based on your actual API structure
        # This is a template - adapt to your real data format

        if isinstance(appro_data, dict) and "total_value" in appro_data:
            metrics["total_stock_value"] = Decimal(str(appro_data["total_value"]))

        if isinstance(sortie_data, dict) and "turnover_rate" in sortie_data:
            metrics["stock_turnover_ratio"] = float(sortie_data["turnover_rate"])

        return metrics

    def _check_budget_alerts(self):
        """Check for budget-related alerts"""
        alerts = []

        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=7),
            budget_variance_percentage__isnull=False,
        )

        for project in recent_projects:
            if (
                abs(project.budget_variance_percentage)
                > self.alert_thresholds["budget_variance"]
            ):
                severity = (
                    "high" if abs(project.budget_variance_percentage) > 20 else "medium"
                )

                alert = AlertSystem.objects.create(
                    alert_type="budget_overrun",
                    severity=severity,
                    title=f"Budget variance detected for {project.project_name}",
                    message=f"Project {project.project_name} has a budget variance of {project.budget_variance_percentage:.1f}%",
                    project_name=project.project_name,
                    threshold_value=self.alert_thresholds["budget_variance"],
                    actual_value=abs(project.budget_variance_percentage),
                    variance_percentage=project.budget_variance_percentage,
                )
                alerts.append(alert)

        return alerts

    def _check_stock_alerts(self):
        """Check for stock-related alerts"""
        alerts = []

        # Get latest stock data and check for shortages
        latest_conso = GStockConsommation.objects.order_by("-created_at").first()

        if latest_conso and isinstance(latest_conso.data, dict):
            # Analyze consumption data for potential shortages
            # This depends on your actual data structure
            consumption_data = latest_conso.data

            # Example logic - adapt to your data
            if "stock_levels" in consumption_data:
                for item, level in consumption_data["stock_levels"].items():
                    if level < self.alert_thresholds["stock_shortage"]:
                        alert = AlertSystem.objects.create(
                            alert_type="stock_shortage",
                            severity="medium",
                            title=f"Low stock alert for {item}",
                            message=f"Stock level for {item} is at {level}%, below threshold",
                            affected_area="Stock Management",
                            threshold_value=self.alert_thresholds["stock_shortage"],
                            actual_value=level,
                        )
                        alerts.append(alert)

        return alerts

    def _check_schedule_alerts(self):
        """Check for schedule-related alerts"""
        alerts = []

        # Check projects for schedule delays
        at_risk_projects = ProjectAnalytics.objects.filter(
            delivery_performance="At-risk",
            analysis_date__gte=timezone.now() - timedelta(days=7),
        )

        for project in at_risk_projects:
            alert = AlertSystem.objects.create(
                alert_type="schedule_delay",
                severity="medium",
                title=f"Schedule risk for {project.project_name}",
                message=f"Project {project.project_name} is at risk of schedule delays",
                project_name=project.project_name,
                actual_value=project.completion_percentage or 0,
            )
            alerts.append(alert)

        return alerts
