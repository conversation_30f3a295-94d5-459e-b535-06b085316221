import pandas as pd
import json
import ast

csv_file = 'G_locative.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

# Création du DataFrame principal directement depuis le CSV
df_biens = df_raw.copy()
print(f"DataFrame créé avec {len(df_biens)} biens")

# Affichage des colonnes disponibles
print("\nColonnes disponibles:")
print(df_biens.columns.tolist())

# Affichage des données de base
print("\n Aperçu des données:")
display(df_biens.head())

class Cleaning:
    def __init__(self, df):
        self.df = df.copy()

    def clean(self):
        # Supprimer colonne id
        self.df.drop(columns=['id'], inplace=True)
        # Renommer colonnes
        self.df.rename(columns={
            'libelle': 'Bien',
            'adresse': 'Adresse',
            'type_id': 'Type_id',
            'created_at': 'Created_at',
            'proprietaires': 'Proprietaires',
            'locataires': 'Locataires',
            'charges': 'Charges',
            'totalCharges': 'TotalCharges',
            'totaLoyer': 'TotalLoyer',
            'totalImpayer': 'TotalImpayer',
            'type': 'Type',
            'actifs': 'Actifs'
        }, inplace=True)
        return self.df

cleaner = Cleaning(df_biens)
df_clean = cleaner.clean()
df_clean.head()

def explode_column(df, col_name, parent_col='Bien'):
    """Éclate une colonne contenant des listes d'objets"""
    rows = []
    
    for _, row in df.iterrows():
        data_list = row.get(col_name)
        
        # Vérifier que c'est une liste non vide
        if isinstance(data_list, str) and data_list.strip() and data_list != '[]':
            try:
                # Parser la chaîne JSON
                data_list = ast.literal_eval(data_list)
            except:
                continue
        
        if isinstance(data_list, list) and len(data_list) > 0:
            for item in data_list:
                if isinstance(item, dict):
                    # Ajouter le nom du bien parent
                    item_copy = item.copy()
                    item_copy[parent_col] = row[parent_col]
                    rows.append(item_copy)
    
    return pd.DataFrame(rows)

# Éclatement des propriétaires
df_proprietaires = explode_column(df_clean, 'Proprietaires')
print(f"Propriétaires extraits: {len(df_proprietaires)}")

# Éclatement des locataires
df_locataires = explode_column(df_clean, 'Locataires')
print(f"Locataires extraits: {len(df_locataires)}")

# Éclatement des charges
df_charges = explode_column(df_clean, 'Charges')
print(f"Charges extraites: {len(df_charges)}")

# Éclatement des actifs
df_actifs = explode_column(df_clean, 'Actifs')
print(f"Actifs extraits: {len(df_actifs)}")

def analyser_proprietaires(bien_nom, df_proprietaires):
    """Analyse et affiche les propriétaires d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les propriétaires pour ce bien
    bien_proprietaires = df_proprietaires[df_proprietaires['Bien'] == bien_nom] if not df_proprietaires.empty else pd.DataFrame()
    
    print(f" Nombre de propriétaires: {len(bien_proprietaires)}")
    print(" Liste des propriétaires:")
    
    # Afficher les informations disponibles
    if not bien_proprietaires.empty and 'user' in bien_proprietaires.columns:
        for _, prop in bien_proprietaires.iterrows():
            user_info = prop.get('user', {})
            if isinstance(user_info, dict):
                print(f"  - {user_info.get('name', 'N/A')} ({user_info.get('email', 'N/A')})")
    else:
        print("  Aucun propriétaire")

def analyser_locataires(bien_nom, df_locataires):
    """Analyse et affiche les locataires d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les locataires pour ce bien
    bien_locataires = df_locataires[df_locataires['Bien'] == bien_nom] if not df_locataires.empty else pd.DataFrame()
    
    print(f" Nombre de locataires: {len(bien_locataires)}")
    print(" Liste des locataires:")
    
    if not bien_locataires.empty:
        for _, loc in bien_locataires.iterrows():
            nom = loc.get('nom', 'N/A')
            prenom = loc.get('prenom', 'N/A')
            contact = loc.get('contact', 'N/A')
            print(f"  - {nom} {prenom} ({contact})")
    else:
        print("  Aucun locataire")

def analyser_charges(bien_nom, charges_data):
    """Analyse et affiche les charges d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    if isinstance(charges_data, str) and charges_data.strip() and charges_data != '[]':
        try:
            charges_list = ast.literal_eval(charges_data)
            if isinstance(charges_list, list) and len(charges_list) > 0:
                print(" Détail des charges:")
                for charge in charges_list:
                    if isinstance(charge, dict):
                        libelle = charge.get('libelle', 'N/A')
                        montant = charge.get('montant', 0)
                        print(f"  - {libelle}: {montant:,.0f} XOF")
            else:
                print(" Aucune charge")
        except:
            print(" Erreur lors du parsing des charges")
    else:
        print(" Aucune charge")

def analyser_actifs(bien_nom, df_actifs):
    """Analyse et affiche les actifs d'un bien"""
    print(f"\n BIEN: {bien_nom}")
    
    # Filtrer les actifs pour ce bien
    bien_actifs = df_actifs[df_actifs['Bien'] == bien_nom] if not df_actifs.empty else pd.DataFrame()
    
    print(f" Nombre d'actifs: {len(bien_actifs)}")
    print(" Liste des actifs:")
    
    if not bien_actifs.empty:
        for _, actif in bien_actifs.iterrows():
            libelle = actif.get('libelle', 'N/A')
            description = actif.get('description', 'N/A')
            type_info = actif.get('type', {})
            type_libelle = type_info.get('libelle', 'N/A') if isinstance(type_info, dict) else 'N/A'
            print(f"  - {libelle} ({type_libelle}) - {description}")
    else:
        print("  Aucun actif")

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_proprietaires(bien_nom, df_proprietaires)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_locataires(bien_nom, df_locataires)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    charges_data = bien_row['Charges']
    analyser_charges(bien_nom, charges_data)

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    type_data = bien_row['Type']
    
    print(f"\n BIEN: {bien_nom}")
    
    if isinstance(type_data, str) and type_data.strip() and type_data != '{}':
        try:
            type_info = ast.literal_eval(type_data)
            if isinstance(type_info, dict):
                type_libelle = type_info.get('libelle', 'N/A')
                print(f" Type: {type_libelle}")
            else:
                print(" Type: N/A")
        except:
            print(" Erreur lors du parsing du type")
    else:
        print(" Type: N/A")

for _, bien_row in df_clean.iterrows():
    bien_nom = bien_row['Bien']
    analyser_actifs(bien_nom, df_actifs)

