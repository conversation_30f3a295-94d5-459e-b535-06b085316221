{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE DES DONNÉES GESTION LOCATIVE\n", "## Liste des imports utiles"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: G_locative.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>libelle</th>\n", "      <th>adresse</th>\n", "      <th>type_id</th>\n", "      <th>created_at</th>\n", "      <th>proprietaires</th>\n", "      <th>locataires</th>\n", "      <th>charges</th>\n", "      <th>totalCharges</th>\n", "      <th>to<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>totalImpayer</th>\n", "      <th>type</th>\n", "      <th>actifs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>57</td>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>8</td>\n", "      <td>2025-04-04 16:55:39</td>\n", "      <td>[{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 60, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>30000</td>\n", "      <td>700000</td>\n", "      <td>730000</td>\n", "      <td>{'id': 8, 'libelle': 'MAGASIN'}</td>\n", "      <td>[{'id': 224, 'libelle': 'CN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>56</td>\n", "      <td>VILLA NADINE</td>\n", "      <td>INOOVIM</td>\n", "      <td>4</td>\n", "      <td>2025-03-28 16:36:37</td>\n", "      <td>[{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>600000</td>\n", "      <td>1800000</td>\n", "      <td>{'id': 4, 'libelle': 'Villa'}</td>\n", "      <td>[{'id': 223, 'libelle': 'VN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>55</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-11-08 08:06:34</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 57, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1200000</td>\n", "      <td>3735000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 219, 'libelle': 'VILLA 28', 'descripti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>54</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>COCODY RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-05-29 17:54:13</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 56, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1150000</td>\n", "      <td>3585000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 218, 'libelle': 'LOT 25', 'description...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>53</td>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>ABATTA CITE DU PORT</td>\n", "      <td>5</td>\n", "      <td>2024-04-25 17:01:05</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>1800000</td>\n", "      <td>3150000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 217, 'libelle': 'VD1', 'description': ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                               libelle                 adresse  type_id  \\\n", "0  57                       COMMERCE NADINE               SYMPHONIA        8   \n", "1  56                          VILLA NADINE                 INOOVIM        4   \n", "2  55  SYMPHONIA VILLA DUPLEX LOT 28 300 M2         RIVIERA EPHRATA        5   \n", "3  54         SYMPHONIA VILLA DUPLEX LOT 25  COCODY RIVIERA EPHRATA        5   \n", "4  53             VILLA DUPLEX CITE DU PORT     ABATTA CITE DU PORT        5   \n", "\n", "            created_at                                      proprietaires  \\\n", "0  2025-04-04 16:55:39  [{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...   \n", "1  2025-03-28 16:36:37  [{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...   \n", "2  2024-11-08 08:06:34                                                 []   \n", "3  2024-05-29 17:54:13                                                 []   \n", "4  2024-04-25 17:01:05                                                 []   \n", "\n", "  locataires                                            charges  totalCharges  \\\n", "0         []  [{'id': 60, 'libelle': 'Charges Locatives', 'm...         30000   \n", "1         []                                                 []             0   \n", "2         []  [{'id': 57, 'libelle': 'Charges Locatives', 'm...         45000   \n", "3         []  [{'id': 56, 'libelle': 'Charges Locatives', 'm...         45000   \n", "4         []                                                 []             0   \n", "\n", "   totaLoyer  totalImpayer                             type  \\\n", "0     700000        730000  {'id': 8, 'libelle': 'MAGASIN'}   \n", "1     600000       1800000    {'id': 4, 'libelle': 'Villa'}   \n", "2    1200000       3735000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "3    1150000       3585000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "4    1800000       3150000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "\n", "                                              actifs  \n", "0  [{'id': 224, 'libelle': 'CN', 'description': N...  \n", "1  [{'id': 223, 'libelle': 'VN', 'description': N...  \n", "2  [{'id': 219, 'libelle': 'VILLA 28', 'descripti...  \n", "3  [{'id': 218, 'libelle': 'LOT 25', 'description...  \n", "4  [{'id': 217, 'libelle': 'VD1', 'description': ...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = 'G_locative.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")    \n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parsing des données et creation du dataframe parent"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame créé avec 42 biens\n", "\n", "Colonnes disponibles:\n", "['id', 'libelle', 'adresse', 'type_id', 'created_at', 'proprietaires', 'locataires', 'charges', 'totalCharges', 'totaLoyer', 'totalImpayer', 'type', 'actifs']\n", "\n", " Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>libelle</th>\n", "      <th>adresse</th>\n", "      <th>type_id</th>\n", "      <th>created_at</th>\n", "      <th>proprietaires</th>\n", "      <th>locataires</th>\n", "      <th>charges</th>\n", "      <th>totalCharges</th>\n", "      <th>to<PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>totalImpayer</th>\n", "      <th>type</th>\n", "      <th>actifs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>57</td>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>8</td>\n", "      <td>2025-04-04 16:55:39</td>\n", "      <td>[{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 60, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>30000</td>\n", "      <td>700000</td>\n", "      <td>730000</td>\n", "      <td>{'id': 8, 'libelle': 'MAGASIN'}</td>\n", "      <td>[{'id': 224, 'libelle': 'CN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>56</td>\n", "      <td>VILLA NADINE</td>\n", "      <td>INOOVIM</td>\n", "      <td>4</td>\n", "      <td>2025-03-28 16:36:37</td>\n", "      <td>[{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>600000</td>\n", "      <td>1800000</td>\n", "      <td>{'id': 4, 'libelle': 'Villa'}</td>\n", "      <td>[{'id': 223, 'libelle': 'VN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>55</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-11-08 08:06:34</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 57, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1200000</td>\n", "      <td>3735000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 219, 'libelle': 'VILLA 28', 'descripti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>54</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>COCODY RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-05-29 17:54:13</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 56, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1150000</td>\n", "      <td>3585000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 218, 'libelle': 'LOT 25', 'description...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>53</td>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>ABATTA CITE DU PORT</td>\n", "      <td>5</td>\n", "      <td>2024-04-25 17:01:05</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>1800000</td>\n", "      <td>3150000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 217, 'libelle': 'VD1', 'description': ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                               libelle                 adresse  type_id  \\\n", "0  57                       COMMERCE NADINE               SYMPHONIA        8   \n", "1  56                          VILLA NADINE                 INOOVIM        4   \n", "2  55  SYMPHONIA VILLA DUPLEX LOT 28 300 M2         RIVIERA EPHRATA        5   \n", "3  54         SYMPHONIA VILLA DUPLEX LOT 25  COCODY RIVIERA EPHRATA        5   \n", "4  53             VILLA DUPLEX CITE DU PORT     ABATTA CITE DU PORT        5   \n", "\n", "            created_at                                      proprietaires  \\\n", "0  2025-04-04 16:55:39  [{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...   \n", "1  2025-03-28 16:36:37  [{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...   \n", "2  2024-11-08 08:06:34                                                 []   \n", "3  2024-05-29 17:54:13                                                 []   \n", "4  2024-04-25 17:01:05                                                 []   \n", "\n", "  locataires                                            charges  totalCharges  \\\n", "0         []  [{'id': 60, 'libelle': 'Charges Locatives', 'm...         30000   \n", "1         []                                                 []             0   \n", "2         []  [{'id': 57, 'libelle': 'Charges Locatives', 'm...         45000   \n", "3         []  [{'id': 56, 'libelle': 'Charges Locatives', 'm...         45000   \n", "4         []                                                 []             0   \n", "\n", "   totaLoyer  totalImpayer                             type  \\\n", "0     700000        730000  {'id': 8, 'libelle': 'MAGASIN'}   \n", "1     600000       1800000    {'id': 4, 'libelle': 'Villa'}   \n", "2    1200000       3735000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "3    1150000       3585000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "4    1800000       3150000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "\n", "                                              actifs  \n", "0  [{'id': 224, 'libelle': 'CN', 'description': N...  \n", "1  [{'id': 223, 'libelle': 'VN', 'description': N...  \n", "2  [{'id': 219, 'libelle': 'VILLA 28', 'descripti...  \n", "3  [{'id': 218, 'libelle': 'LOT 25', 'description...  \n", "4  [{'id': 217, 'libelle': 'VD1', 'description': ...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création du DataFrame principal directement depuis le CSV\n", "df_biens = df_raw.copy()\n", "print(f\"DataFrame créé avec {len(df_biens)} biens\")\n", "\n", "# Affichage des colonnes disponibles\n", "print(\"\\nColonnes disponibles:\")\n", "print(df_biens.columns.tolist())\n", "\n", "# Affichage des données de base\n", "print(\"\\n Aperçu des données:\")\n", "display(df_biens.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type_id</th>\n", "      <th>Created_at</th>\n", "      <th>Pro<PERSON><PERSON>taires</th>\n", "      <th>Locataires</th>\n", "      <th>Charges</th>\n", "      <th>TotalCharges</th>\n", "      <th>TotalLoyer</th>\n", "      <th>TotalImpayer</th>\n", "      <th>Type</th>\n", "      <th>Actifs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>8</td>\n", "      <td>2025-04-04 16:55:39</td>\n", "      <td>[{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 60, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>30000</td>\n", "      <td>700000</td>\n", "      <td>730000</td>\n", "      <td>{'id': 8, 'libelle': 'MAGASIN'}</td>\n", "      <td>[{'id': 224, 'libelle': 'CN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VILLA NADINE</td>\n", "      <td>INOOVIM</td>\n", "      <td>4</td>\n", "      <td>2025-03-28 16:36:37</td>\n", "      <td>[{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>600000</td>\n", "      <td>1800000</td>\n", "      <td>{'id': 4, 'libelle': 'Villa'}</td>\n", "      <td>[{'id': 223, 'libelle': 'VN', 'description': N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-11-08 08:06:34</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 57, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1200000</td>\n", "      <td>3735000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 219, 'libelle': 'VILLA 28', 'descripti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>COCODY RIVIERA EPHRATA</td>\n", "      <td>5</td>\n", "      <td>2024-05-29 17:54:13</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'id': 56, 'libelle': 'Charges Locatives', 'm...</td>\n", "      <td>45000</td>\n", "      <td>1150000</td>\n", "      <td>3585000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 218, 'libelle': 'LOT 25', 'description...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>ABATTA CITE DU PORT</td>\n", "      <td>5</td>\n", "      <td>2024-04-25 17:01:05</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "      <td>1800000</td>\n", "      <td>3150000</td>\n", "      <td>{'id': 5, 'libelle': 'DUPLEX'}</td>\n", "      <td>[{'id': 217, 'libelle': 'VD1', 'description': ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   Bien                 Adresse  Type_id  \\\n", "0                       COMMERCE NADINE               SYMPHONIA        8   \n", "1                          VILLA NADINE                 INOOVIM        4   \n", "2  SYMPHONIA VILLA DUPLEX LOT 28 300 M2         RIVIERA EPHRATA        5   \n", "3         SYMPHONIA VILLA DUPLEX LOT 25  COCODY RIVIERA EPHRATA        5   \n", "4             VILLA DUPLEX CITE DU PORT     ABATTA CITE DU PORT        5   \n", "\n", "            Created_at                                      Proprie<PERSON><PERSON>  \\\n", "0  2025-04-04 16:55:39  [{'id': 61, 'user_id': 65, 'bien_id': 57, 'tan...   \n", "1  2025-03-28 16:36:37  [{'id': 60, 'user_id': 45, 'bien_id': 56, 'tan...   \n", "2  2024-11-08 08:06:34                                                 []   \n", "3  2024-05-29 17:54:13                                                 []   \n", "4  2024-04-25 17:01:05                                                 []   \n", "\n", "  Locataires                                            Charges  TotalCharges  \\\n", "0         []  [{'id': 60, 'libelle': 'Charges Locatives', 'm...         30000   \n", "1         []                                                 []             0   \n", "2         []  [{'id': 57, 'libelle': 'Charges Locatives', 'm...         45000   \n", "3         []  [{'id': 56, 'libelle': 'Charges Locatives', 'm...         45000   \n", "4         []                                                 []             0   \n", "\n", "   TotalLoyer  TotalImpayer                             Type  \\\n", "0      700000        730000  {'id': 8, 'libelle': 'MAGASIN'}   \n", "1      600000       1800000    {'id': 4, 'libelle': 'Villa'}   \n", "2     1200000       3735000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "3     1150000       3585000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "4     1800000       3150000   {'id': 5, 'libelle': 'DUPLEX'}   \n", "\n", "                                              Actifs  \n", "0  [{'id': 224, 'libelle': 'CN', 'description': N...  \n", "1  [{'id': 223, 'libelle': 'VN', 'description': N...  \n", "2  [{'id': 219, 'libelle': 'VILLA 28', 'descripti...  \n", "3  [{'id': 218, 'libelle': 'LOT 25', 'description...  \n", "4  [{'id': 217, 'libelle': 'VD1', 'description': ...  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["class Cleaning:\n", "    def __init__(self, df):\n", "        self.df = df.copy()\n", "\n", "    def clean(self):\n", "        # Supprimer colonne id\n", "        self.df.drop(columns=['id'], inplace=True)\n", "        # Renommer colonnes\n", "        self.df.rename(columns={\n", "            'libelle': 'Bien',\n", "            'adresse': '<PERSON>ress<PERSON>',\n", "            'type_id': 'Type_id',\n", "            'created_at': 'Created_at',\n", "            'proprietaires': 'Proprietaires',\n", "            'locataires': 'Locataires',\n", "            'charges': 'Charges',\n", "            'totalCharges': 'TotalCharges',\n", "            'totaLoyer': 'TotalLoyer',\n", "            'totalImpayer': 'TotalImpayer',\n", "            'type': 'Type',\n", "            'actifs': 'Actifs'\n", "        }, inplace=True)\n", "        return self.df\n", "\n", "cleaner = Cleaning(df_biens)\n", "df_clean = cleaner.clean()\n", "df_clean.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonction eclatement"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def explode_column(df, col_name, parent_col='Bien'):\n", "    \"\"\"Éclate une colonne contenant des listes d'objets\"\"\"\n", "    rows = []\n", "    \n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        \n", "        # Vérifier que c'est une liste non vide\n", "        if isinstance(data_list, str) and data_list.strip() and data_list != '[]':\n", "            try:\n", "                # Parser la chaîne JSON\n", "                data_list = ast.literal_eval(data_list)\n", "            except:\n", "                continue\n", "        \n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    # Ajouter le nom du bien parent\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    \n", "    return pd.DataFrame(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Éclatement des données"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Propriétaires extraits: 34\n", "Locataires extraits: 10\n", "Charges extraites: 19\n", "Actifs extraits: 162\n"]}], "source": ["# Éclatement des propriétaires\n", "df_proprietaires = explode_column(df_clean, 'Proprietaires')\n", "print(f\"Propriétaires extraits: {len(df_proprietaires)}\")\n", "\n", "# Éclatement des locataires\n", "df_locataires = explode_column(df_clean, 'Locataires')\n", "print(f\"Locataires extraits: {len(df_locataires)}\")\n", "\n", "# Éclatement des charges\n", "df_charges = explode_column(df_clean, 'Charges')\n", "print(f\"Charges extraites: {len(df_charges)}\")\n", "\n", "# Éclatement des actifs\n", "df_actifs = explode_column(df_clean, 'Actifs')\n", "print(f\"Actifs extraits: {len(df_actifs)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions d'analyse"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def analyser_proprietaires(bien_nom, df_proprietaires):\n", "    \"\"\"Analyse et affiche les propriétaires d'un bien\"\"\"\n", "    print(f\"\\n BIEN: {bien_nom}\")\n", "    \n", "    # Filtrer les propriétaires pour ce bien\n", "    bien_proprietaires = df_proprietaires[df_proprietaires['Bien'] == bien_nom] if not df_proprietaires.empty else pd.DataFrame()\n", "    \n", "    print(f\" Nombre de propriétaires: {len(bien_proprietaires)}\")\n", "    print(\" Liste des propriétaires:\")\n", "    \n", "    # C<PERSON>er un DataFrame pour l'affichage\n", "    if not bien_proprietaires.empty and 'user' in bien_proprietaires.columns:\n", "        proprietaires_data = []\n", "        for _, prop in bien_proprietaires.iterrows():\n", "            user_info = prop.get('user', {})\n", "            if isinstance(user_info, dict):\n", "                proprietaires_data.append({\n", "                    'Nom': user_info.get('name', 'N/A'),\n", "                    'Email': user_info.get('email', 'N/A'),\n", "                    'Role': user_info.get('role_id', 'N/A'),\n", "                    'Tantieme': prop.get('tantieme', 'N/A'),\n", "                    'Type': prop.get('type_user', 'N/A')\n", "                })\n", "        \n", "        if proprietaires_data:\n", "            df_display = pd.DataFrame(proprietaires_data)\n", "            display(df_display.reset_index(drop=True))\n", "        else:\n", "            df_empty = pd.DataFrame(columns=['Nom', 'Email', 'Role', 'Tantieme', 'Type'])\n", "            display(df_empty)\n", "    else:\n", "        df_empty = pd.DataFrame(columns=['Nom', 'Email', 'Role', 'Tantieme', 'Type'])\n", "        display(df_empty)\n", "\n", "def analyser_locataires(bien_nom, df_locataires):\n", "    \"\"\"Analyse et affiche les locataires d'un bien\"\"\"\n", "    print(f\"\\n BIEN: {bien_nom}\")\n", "    \n", "    # Filtrer les locataires pour ce bien\n", "    bien_locataires = df_locataires[df_locataires['Bien'] == bien_nom] if not df_locataires.empty else pd.DataFrame()\n", "    \n", "    print(f\" Nombre de locataires: {len(bien_locataires)}\")\n", "    print(\" Liste des locataires:\")\n", "    \n", "    # C<PERSON>er un DataFrame pour l'affichage\n", "    if not bien_locataires.empty:\n", "        locataires_data = []\n", "        for _, loc in bien_locataires.iterrows():\n", "            locataires_data.append({\n", "                'Nom': loc.get('nom', 'N/A'),\n", "                'Prenom': loc.get('prenom', 'N/A'),\n", "                'Email': loc.get('email', 'N/A'),\n", "                'Contact': loc.get('contact', 'N/A'),\n", "                'Indicatif': loc.get('indicatif', 'N/A'),\n", "                'NCC': loc.get('ncc', 'N/A'),\n", "                'Secteur': loc.get('secteur', 'N/A')\n", "            })\n", "        \n", "        df_display = pd.DataFrame(locataires_data)\n", "        display(df_display.reset_index(drop=True))\n", "    else:\n", "        df_empty = pd.DataFrame(columns=['Nom', 'Prenom', 'Email', 'Contact', 'Indicatif', 'NCC', 'Secteur'])\n", "        display(df_empty)\n", "\n", "def analyser_charges(bien_nom, charges_data):\n", "    \"\"\"Analyse et affiche les charges d'un bien\"\"\"\n", "    print(f\"\\n BIEN: {bien_nom}\")\n", "    \n", "    print(\" Détail des charges:\")\n", "    \n", "    # C<PERSON>er un DataFrame pour l'affichage\n", "    if isinstance(charges_data, str) and charges_data.strip() and charges_data != '[]':\n", "        try:\n", "            charges_list = ast.literal_eval(charges_data)\n", "            if isinstance(charges_list, list) and len(charges_list) > 0:\n", "                charges_data_list = []\n", "                for charge in charges_list:\n", "                    if isinstance(charge, dict):\n", "                        charges_data_list.append({\n", "                            'ID': charge.get('id', 'N/A'),\n", "                            'Libelle': charge.get('libelle', 'N/A'),\n", "                            'Montant (XOF)': f\"{charge.get('montant', 0):,.0f}\"\n", "                        })\n", "                \n", "                if charges_data_list:\n", "                    df_display = pd.DataFrame(charges_data_list)\n", "                    display(df_display.reset_index(drop=True))\n", "                else:\n", "                    df_empty = pd.DataFrame(columns=['ID', '<PERSON>belle', '<PERSON><PERSON> (XOF)'])\n", "                    display(df_empty)\n", "            else:\n", "                df_empty = pd.DataFrame(columns=['ID', '<PERSON>belle', '<PERSON><PERSON> (XOF)'])\n", "                display(df_empty)\n", "        except:\n", "            print(\" Erreur lors du parsing des charges\")\n", "            df_empty = pd.DataFrame(columns=['ID', '<PERSON>belle', '<PERSON><PERSON> (XOF)'])\n", "            display(df_empty)\n", "    else:\n", "        df_empty = pd.DataFrame(columns=['ID', '<PERSON>belle', '<PERSON><PERSON> (XOF)'])\n", "        display(df_empty)\n", "\n", "def analyser_actifs(bien_nom, df_actifs):\n", "    \"\"\"Analyse et affiche les actifs d'un bien\"\"\"\n", "    print(f\"\\n BIEN: {bien_nom}\")\n", "    \n", "    # Filtrer les actifs pour ce bien\n", "    bien_actifs = df_actifs[df_actifs['Bien'] == bien_nom] if not df_actifs.empty else pd.DataFrame()\n", "    \n", "    print(f\" Nombre d'actifs: {len(bien_actifs)}\")\n", "    print(\" Liste des actifs:\")\n", "    \n", "    # C<PERSON>er un DataFrame pour l'affichage\n", "    if not bien_actifs.empty:\n", "        actifs_data = []\n", "        for _, actif in bien_actifs.iterrows():\n", "            type_info = actif.get('type', {})\n", "            type_libelle = type_info.get('libelle', 'N/A') if isinstance(type_info, dict) else 'N/A'\n", "            actifs_data.append({\n", "                'ID': actif.get('id', 'N/A'),\n", "                'Libelle': actif.get('libelle', 'N/A'),\n", "                'Description': actif.get('description', 'N/A'),\n", "                'Type': type_libelle,\n", "                'Type_ID': actif.get('type_id', 'N/A')\n", "            })\n", "        \n", "        df_display = pd.DataFrame(actifs_data)\n", "        display(df_display.reset_index(drop=True))\n", "    else:\n", "        df_empty = pd.DataFrame(columns=['ID', 'Libelle', 'Description', 'Type', 'Type_ID'])\n", "        display(df_empty)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Propriétaires"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: COMMERCE NADINE\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>YAO</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Nom                Email  Role Tantieme Type\n", "0  YAO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA NADINE\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MARIKO</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Nom               Email  Role Tantieme Type\n", "0  MARIKO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 28 300 M2\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 25\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA DUPLEX CITE DU PORT\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VIILA DUPLEX LOT 49\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MOUMOUNI EPSE DJIBRILLA</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       Nom                           Email  Role Tantieme Type\n", "0  MOUMOUNI EPSE DJIBRILLA  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 104\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI PORTHOS</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Nom                  Email  Role Tantieme Type\n", "0  SCI PORTHOS  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 75\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI PORTHOS</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Nom                  Email  Role Tantieme Type\n", "0  SCI PORTHOS  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 51\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI PORTHOS</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Nom                  Email  Role Tantieme Type\n", "0  SCI PORTHOS  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 20\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N'GUESSAN</td>\n", "      <td>marie-jose<PERSON>.ng<PERSON><PERSON>@bni.ci</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                        Email  Role Tantieme Type\n", "0  N'GUESSAN  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 78\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZABALOU</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom              Email  Role Tantieme Type\n", "0  ZABALOU  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC E\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI AMANDUE</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Nom                  Email  Role Tantieme Type\n", "0  SCI AMANDUE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA 45\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC D\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI SYECA</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                 Email  Role Tantieme Type\n", "0  SCI SYECA  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC C\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI GNENEYOMA</td>\n", "      <td>scigneney<PERSON>@gmail.com</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Nom                   Email  Role Tantieme Type\n", "0  SCI GNENEYOMA  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC B\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI HARITH / SCI ATHOS</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Nom                Email  Role Tantieme Type\n", "0  SCI HARITH / SCI ATHOS  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC A\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI CAMILLA / SCI ATHOS</td>\n", "      <td>scicam<PERSON>@gmail.com</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       Nom                 Email  Role Tantieme Type\n", "0  SCI CAMILLA / SCI ATHOS  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 14\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ALLA EPSE YAO BI</td>\n", "      <td>j<PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Nom                Email  Role Tantieme Type\n", "0  ALLA EPSE YAO BI  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LES CONCESSIONS DE BINGERVILLES\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 94\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BOURGOIN</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Nom                   Email  Role Tantieme Type\n", "0  BOURGOIN  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 83 ILOT 09\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI SYECA</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                 Email  Role Tantieme Type\n", "0  SCI SYECA  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 48\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AGNIMEL</td>\n", "      <td>agnimel.anastasiel<PERSON><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom                             Email  Role Tantieme Type\n", "0  AGNIMEL  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 21\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SANGARE</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom               Email  Role Tantieme Type\n", "0  SANGARE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 85\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Nom                            Email  Role Tantieme Type\n", "0  SCI  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 107\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COULIBALY</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                      Email  Role Tantieme Type\n", "0  COULIBALY  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 37\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 101\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KOUADIO</td>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@yahou.com</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom                         Email  Role Tantieme Type\n", "0  KOUADIO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 15 LOT 275\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>OUEDRAOGO</td>\n", "      <td>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                      Email  Role Tantieme Type\n", "0  OUEDRAOGO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 68\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZABALOU</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom              Email  Role Tantieme Type\n", "0  ZABALOU  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 59\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GOSSE</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Nom                                    Email  Role Tantieme Type\n", "0  GOSSE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA  IMMEUBLE LOT 68\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N'DAKPRI</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Nom                               Email  Role Tantieme Type\n", "0  N'DAKPRI  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 5 LOT 64\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SCI ARDO EGIDE</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Nom                     Email  Role Tantieme Type\n", "0  SCI ARDO EGIDE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 86\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Mme TANO</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Nom                           Email  Role Tantieme Type\n", "0  Mme TANO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 97\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SANGARE</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom               Email  Role Tantieme Type\n", "0  SANGARE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 2 LOT 106\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KOUADIO</td>\n", "      <td>j<PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Nom                Email  Role Tantieme Type\n", "0  KOUADIO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: M'BADON VILLA\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KRA KOFFI</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom                Email  Role Tantieme Type\n", "0  KRA KOFFI  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA LOT 79\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE CISSE PORT BOUET\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CISSE</td>\n", "      <td>y<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Nom                    Email  Role Tantieme Type\n", "0  CISSE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: <PERSON>MM<PERSON>UBL<PERSON> CISSE JULES VERNE\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CISSE</td>\n", "      <td>y<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Nom                    Email  Role Tantieme Type\n", "0  CISSE  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: L'IMMEUBLE DE BIETRY SCI KAD\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MARIKO</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Nom               Email  Role Tantieme Type\n", "0  MARIKO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE SCI KAD REMBLAIS\n", " Nombre de propriétaires: 1\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MARIKO</td>\n", "      <td><EMAIL></td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Nom               Email  Role Tantieme Type\n", "0  MARIKO  <EMAIL>     3     None    3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: EGDGDG\n", " Nombre de propriétaires: 0\n", " Liste des propriétaires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Email</th>\n", "      <th>Role</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, bien_row in df_clean.iterrows():\n", "    bien_nom = bien_row['Bien']\n", "    analyser_proprietaires(bien_nom, df_proprietaires)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Locataires"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: COMMERCE NADINE\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA NADINE\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 28 300 M2\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 25\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA DUPLEX CITE DU PORT\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VIILA DUPLEX LOT 49\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 104\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 75\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 51\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 20\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 78\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC E\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA 45\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC D\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC C\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC B\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC A\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 14\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TSHEKABU</td>\n", "      <td>GISELE</td>\n", "      <td>None</td>\n", "      <td>0799886285</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Nom  Prenom Email     Contact Indicatif   NCC Secteur\n", "0  TSHEKABU  GISELE  None  0799886285       225  None    None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LES CONCESSIONS DE BINGERVILLES\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 94\n", " Nombre de locataires: 2\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CHEHAB CI</td>\n", "      <td>None</td>\n", "      <td>j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>0777853438</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PICOT</td>\n", "      <td>PAUL</td>\n", "      <td>None</td>\n", "      <td>00254 743 70 98 45</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom Prenom                        Email             Contact  \\\n", "0  CHEHAB CI   None  <EMAIL>          0777853438   \n", "1      PICOT   PAUL                         None  00254 743 70 98 45   \n", "\n", "  Indicatif   NCC Secteur  \n", "0       225  None    None  \n", "1       225  None    None  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 83 ILOT 09\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CHEHAB CI</td>\n", "      <td>None</td>\n", "      <td>j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@yahoo.fr</td>\n", "      <td>0777853438</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Nom Prenom                         Email     Contact Indicatif   NCC  \\\n", "0  CHEHAB CI   None  jean<PERSON><PERSON><PERSON><EMAIL>  0777853438       225  None   \n", "\n", "  <PERSON><PERSON><PERSON>  \n", "0    None  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 48\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SORO</td>\n", "      <td>AWA</td>\n", "      <td><EMAIL></td>\n", "      <td>07 48 33 14 85</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Nom Prenom              Email         Contact Indicatif   NCC Secteur\n", "0  SORO    AWA  <EMAIL>  07 48 33 14 85       225  None    None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 21\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ELIKA</td>\n", "      <td>DOUGNA EPSE ABBA</td>\n", "      <td>None</td>\n", "      <td>0585057809</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Nom            Prenom Email     Contact Indicatif   NCC Secteur\n", "0  ELIKA  DOUGNA EPSE ABBA  None  0585057809       225  None    None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 85\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 107\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 37\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 101\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 15 LOT 275\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 68\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 59\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INSTITUT GEORGETTE</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0708089203</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Nom Prenom Email     Contact Indicatif   NCC Secteur\n", "0  INSTITUT GEORGETTE   None  None  0708089203       225  None    None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA  IMMEUBLE LOT 68\n", " Nombre de locataires: 2\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>REDCO</td>\n", "      <td>HABITATION</td>\n", "      <td><EMAIL></td>\n", "      <td>0758653133</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>B<PERSON>L<PERSON>'S PATISSERIE</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>07 09 95 96 24</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Nom      Prenom                      Email         Contact  \\\n", "0               REDCO  HABITATION  <EMAIL>      0758653133   \n", "1  BOLLY'S PATISSERIE        None                       None  07 09 95 96 24   \n", "\n", "  Indicatif   NCC Secteur  \n", "0       225  None    None  \n", "1       225  None    None  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 5 LOT 64\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 86\n", " Nombre de locataires: 1\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AMIAN</td>\n", "      <td>KOUASSI</td>\n", "      <td>None</td>\n", "      <td>+19046017969</td>\n", "      <td>225</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Nom   Prenom Email       Contact Indicatif   NCC Secteur\n", "0  AMIAN  KOUASSI  None  +19046017969       225  None    None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 97\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 2 LOT 106\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: M'BADON VILLA\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA LOT 79\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE CISSE PORT BOUET\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: <PERSON>MM<PERSON>UBL<PERSON> CISSE JULES VERNE\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: L'IMMEUBLE DE BIETRY SCI KAD\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE SCI KAD REMBLAIS\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: EGDGDG\n", " Nombre de locataires: 0\n", " Liste des locataires:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nom</th>\n", "      <th>Prenom</th>\n", "      <th>Email</th>\n", "      <th>Contact</th>\n", "      <th>Indicatif</th>\n", "      <th>NCC</th>\n", "      <th>Secteur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON>m, Email, Contact, Indicatif, NCC, Secteur]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, bien_row in df_clean.iterrows():\n", "    bien_nom = bien_row['Bien']\n", "    analyser_locataires(bien_nom, df_locataires)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Charges"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: COMMERCE NADINE\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA NADINE\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 28 300 M2\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 25\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA DUPLEX CITE DU PORT\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VIILA DUPLEX LOT 49\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 104\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 75\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 51\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 20\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 78\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC E\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA 45\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC D\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC C\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC B\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC A\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 14\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LES CONCESSIONS DE BINGERVILLES\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 94\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 83 ILOT 09\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 48\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 21\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 85\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 107\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 37\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 101\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 15 LOT 275\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 68\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 59\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA  IMMEUBLE LOT 68\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 5 LOT 64\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 86\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 97\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 2 LOT 106\n", " <PERSON><PERSON><PERSON> des charges:\n", " Erreur lors du parsing des charges\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: M'BADON VILLA\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA LOT 79\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE CISSE PORT BOUET\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: <PERSON>MM<PERSON>UBL<PERSON> CISSE JULES VERNE\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: L'IMMEUBLE DE BIETRY SCI KAD\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE SCI KAD REMBLAIS\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: EGDGDG\n", " <PERSON><PERSON><PERSON> des charges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Montant (XOF)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (XOF)]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, bien_row in df_clean.iterrows():\n", "    bien_nom = bien_row['Bien']\n", "    charges_data = bien_row['Charges']\n", "    analyser_charges(bien_nom, charges_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Types"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Résumé des types de biens (42 biens):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Type_ID</th>\n", "      <th>Type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>8</td>\n", "      <td>MAGASIN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VILLA NADINE</td>\n", "      <td>4</td>\n", "      <td>Villa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SYMPHONIA VIILA DUPLEX LOT 49</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 104</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 75</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 51</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>LAURIERS 20</td>\n", "      <td>4</td>\n", "      <td>Villa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>SYMPHONIA LOT 78</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>SYMPHONIUM BLOC E</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>SYMPHONIA VILLA 45</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>SYMPHONIUM BLOC D</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>SYMPHONIUM BLOC C</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>SYMPHONIUM BLOC B</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>SYMPHONIUM BLOC A</td>\n", "      <td>6</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>SYMPHONIA LOT 14</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>LES CONCESSIONS DE BINGERVILLES</td>\n", "      <td>9</td>\n", "      <td>CONCESSION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>SYMPHONIA DUPLEX LOT 94</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>SYMPHONIA DUPLEX LOT 83 ILOT 09</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 48</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>SYMPHONIA DUPLEX LOT 21</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>SYMPHONIA DUPLEX LOT 85</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>SYMPHONIA DUPLEX LOT 107</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>SYMPHONIA DUPLEX LOT 37</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>SYMPHONIA IMMEUBLE LOT 101</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>LAURIERS 15 LOT 275</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>SYMPHONIA IMMEUBLE LOT 68</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>SYMPHONIA IMMEUBLE LOT 59</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>SYMPHONIA  IMMEUBLE LOT 68</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>SYMPHONIA VILLA DUPLEX 5 LOT 64</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 86</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 97</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>SYMPHONIA VILLA DUPLEX 2 LOT 106</td>\n", "      <td>5</td>\n", "      <td>DUPLEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>M'BADON VILLA</td>\n", "      <td>4</td>\n", "      <td>Villa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>SYMPHONIA VILLA LOT 79</td>\n", "      <td>4</td>\n", "      <td>Villa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>IMMEUBLE CISSE PORT BOUET</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>IMMEUBLE CISSE JULES VERNE</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>L'IMMEUBLE DE BIETRY SCI KAD</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>IMMEUBLE SCI KAD REMBLAIS</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>EGDGDG</td>\n", "      <td>1</td>\n", "      <td>Imme<PERSON>le</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    Bien  Type_ID             Type\n", "0                        COMMERCE NADINE        8          MAGASIN\n", "1                           VILLA NADINE        4            Villa\n", "2   SYMPHONIA VILLA DUPLEX LOT 28 300 M2        5           DUPLEX\n", "3          SYMPHONIA VILLA DUPLEX LOT 25        5           DUPLEX\n", "4              VILLA DUPLEX CITE DU PORT        5           DUPLEX\n", "5          SYMPHONIA VIILA DUPLEX LOT 49        5           DUPLEX\n", "6      SYMPHONIA RIVIERA EPHRATA LOT 104        5           DUPLEX\n", "7       SYMPHONIA RIVIERA EPHRATA LOT 75        5           DUPLEX\n", "8       SYMPHONIA RIVIERA EPHRATA LOT 51        6  BLOC D'IMMEUBLE\n", "9                            LAURIERS 20        4            Villa\n", "10                      SYMPHONIA LOT 78        6  BLOC D'IMMEUBLE\n", "11                     SYMPHONIUM BLOC E        6  BLOC D'IMMEUBLE\n", "12                    SYMPHONIA VILLA 45        5           DUPLEX\n", "13                     SYMPHONIUM BLOC D        6  BLOC D'IMMEUBLE\n", "14                     SYMPHONIUM BLOC C        6  BLOC D'IMMEUBLE\n", "15                     SYMPHONIUM BLOC B        6  BLOC D'IMMEUBLE\n", "16                     SYMPHONIUM BLOC A        6  BLOC D'IMMEUBLE\n", "17                      SYMPHONIA LOT 14        5           DUPLEX\n", "18       LES CONCESSIONS DE BINGERVILLES        9       CONCESSION\n", "19               SYMPHONIA DUPLEX LOT 94        5           DUPLEX\n", "20       SYMPHONIA DUPLEX LOT 83 ILOT 09        5           DUPLEX\n", "21         SYMPHONIA VILLA DUPLEX LOT 48        5           DUPLEX\n", "22               SYMPHONIA DUPLEX LOT 21        5           DUPLEX\n", "23               SYMPHONIA DUPLEX LOT 85        5           DUPLEX\n", "24              SYMPHONIA DUPLEX LOT 107        5           DUPLEX\n", "25               SYMPHONIA DUPLEX LOT 37        5           DUPLEX\n", "26            SYMPHONIA IMMEUBLE LOT 101        1         Immeuble\n", "27                   LAURIERS 15 LOT 275        1         Immeuble\n", "28             SYMPHONIA IMMEUBLE LOT 68        1         Immeuble\n", "29             SYMPHONIA IMMEUBLE LOT 59        1         Immeuble\n", "30            SYMPHONIA  IMMEUBLE LOT 68        1         Immeuble\n", "31       SYMPHONIA VILLA DUPLEX 5 LOT 64        5           DUPLEX\n", "32        SYMPHONIA VILLA DUPLEX  LOT 86        5           DUPLEX\n", "33        SYMPHONIA VILLA DUPLEX  LOT 97        5           DUPLEX\n", "34      SYMPHONIA VILLA DUPLEX 2 LOT 106        5           DUPLEX\n", "35                         M'BADON VILLA        4            Villa\n", "36                SYMPHONIA VILLA LOT 79        4            Villa\n", "37             IMMEUBLE CISSE PORT BOUET        1         Immeuble\n", "38            IMMEUBLE CISSE JULES VERNE        1         Immeuble\n", "39          L'IMMEUBLE DE BIETRY SCI KAD        1         Immeuble\n", "40             IMMEUBLE SCI KAD REMBLAIS        1         Immeuble\n", "41                                EGDGDG        1         Immeuble"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Répartition par type:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type</th>\n", "      <th>Nombre de biens</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DUPLEX</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Villa</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MAGASIN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>CONCESSION</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Type  Nombre de biens\n", "0           DUPLEX               19\n", "1         Immeuble               10\n", "2  BLOC D'IMMEUBLE                7\n", "3            Villa                4\n", "4          MAGASIN                1\n", "5       CONCESSION                1"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# C<PERSON>er un DataFrame avec tous les types de biens\n", "types_data = []\n", "\n", "for _, bien_row in df_clean.iterrows():\n", "    bien_nom = bien_row['Bien']\n", "    type_data = bien_row['Type']\n", "    \n", "    if isinstance(type_data, str) and type_data.strip() and type_data != '{}':\n", "        try:\n", "            type_info = ast.literal_eval(type_data)\n", "            if isinstance(type_info, dict):\n", "                types_data.append({\n", "                    'Bien': bien_nom,\n", "                    'Type_ID': type_info.get('id', 'N/A'),\n", "                    'Type': type_info.get('libelle', 'N/A')\n", "                })\n", "            else:\n", "                types_data.append({\n", "                    'Bien': bien_nom,\n", "                    'Type_ID': 'N/A',\n", "                    'Type': 'N/A'\n", "                })\n", "        except:\n", "            types_data.append({\n", "                'Bien': bien_nom,\n", "                'Type_ID': 'N/A',\n", "                'Type': 'Erreur parsing'\n", "            })\n", "    else:\n", "        types_data.append({\n", "            'Bien': bien_nom,\n", "            'Type_ID': 'N/A',\n", "            'Type': 'N/A'\n", "        })\n", "\n", "# Afficher le DataFrame des types\n", "if types_data:\n", "    df_types = pd.DataFrame(types_data)\n", "    print(f\"\\nRésumé des types de biens ({len(df_types)} biens):\")\n", "    display(df_types.reset_index(drop=True))\n", "    \n", "    # Statistiques par type\n", "    print(\"\\nRépartition par type:\")\n", "    type_counts = df_types['Type'].value_counts().reset_index()\n", "    type_counts.columns = ['Type', 'Nombre de biens']\n", "    display(type_counts)\n", "else:\n", "    print(\"Aucune donnée de type disponible\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Affichage Actifs"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: COMMERCE NADINE\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>224</td>\n", "      <td>CN</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description     Type  Type_ID\n", "0  224      CN        None  Magasin        3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA NADINE\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>223</td>\n", "      <td>VN</td>\n", "      <td>None</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  223      VN        None  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 28 300 M2\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>219</td>\n", "      <td>VILLA 28</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID   Libelle Description    Type  Type_ID\n", "0  219  VILLA 28    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 25\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>218</td>\n", "      <td>LOT 25</td>\n", "      <td>5 PIECES</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  218  LOT 25    5 PIECES  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: VILLA DUPLEX CITE DU PORT\n", " Nombre d'actifs: 3\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>217</td>\n", "      <td>VD1</td>\n", "      <td>7 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>220</td>\n", "      <td>VB2</td>\n", "      <td>3 PIECES BASSE</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>221</td>\n", "      <td>VB1</td>\n", "      <td>3 PIECES BASSE</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle     Description    Type  Type_ID\n", "0  217     VD1        7 PIECES  DUPLEX        5\n", "1  220     VB2  3 PIECES BASSE   VILLA        4\n", "2  221     VB1  3 PIECES BASSE   VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VIILA DUPLEX LOT 49\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>216</td>\n", "      <td>LOT 49</td>\n", "      <td>5 PIECES</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  216  LOT 49    5 PIECES  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 104\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>211</td>\n", "      <td>LOT 104</td>\n", "      <td>3 PIECES</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID  Libelle Description   Type  Type_ID\n", "0  211  LOT 104    3 PIECES  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 75\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210</td>\n", "      <td>LOT 75</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  210  LOT 75    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA RIVIERA EPHRATA LOT 51\n", " Nombre d'actifs: 2\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>208</td>\n", "      <td>APP A</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>209</td>\n", "      <td>APP B</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0  208   APP A    3 PIECES  Appartement        1\n", "1  209   APP B    3 PIECES  Appartement        1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 20\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>204</td>\n", "      <td>Villa</td>\n", "      <td>4 Pieces</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  204   Villa    4 Pieces  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 78\n", " Nombre d'actifs: 2\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>202</td>\n", "      <td>M78</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>203</td>\n", "      <td>LOT 78</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0  202     M78        None      Magasin        3\n", "1  203  LOT 78    3 PIECES  Appartement        1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC E\n", " Nombre d'actifs: 2\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>213</td>\n", "      <td>E4</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>222</td>\n", "      <td>ME</td>\n", "      <td>MEZZANINE</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0  213      E4    3 PIECES  Appartement        1\n", "1  222      ME   MEZZANINE      Magasin        3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA 45\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>199</td>\n", "      <td>Charges Locatives</td>\n", "      <td>None</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID            Libelle Description   Type  Type_ID\n", "0  199  Charges Locatives        None  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC D\n", " Nombre d'actifs: 8\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>191</td>\n", "      <td>MD1</td>\n", "      <td>RDC 36,35 M²/ MEZZANINE 20,52 M²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>192</td>\n", "      <td>MD2</td>\n", "      <td>RDC 36,35 M² / MEZZANINE 20,52 M²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>193</td>\n", "      <td>MD3</td>\n", "      <td>RDC 36,35 M² / MZZANINE 20,52 M²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>194</td>\n", "      <td>MD4</td>\n", "      <td>RDC 36,35 M² / MEZZANINE 20,52 M²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>195</td>\n", "      <td>D1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>196</td>\n", "      <td>D2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>197</td>\n", "      <td>D3</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>198</td>\n", "      <td>D4</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle                        Description     Type  Type_ID\n", "0  191     MD1   RDC 36,35 M²/ MEZZANINE 20,52 M²  Magasin        3\n", "1  192     MD2  RDC 36,35 M² / MEZZANINE 20,52 M²  Magasin        3\n", "2  193     MD3   RDC 36,35 M² / MZZANINE 20,52 M²  Magasin        3\n", "3  194     MD4  RDC 36,35 M² / MEZZANINE 20,52 M²  Magasin        3\n", "4  195      D1                           3 PIECES   Bureau        2\n", "5  196      D2                           3 PIECES   Bureau        2\n", "6  197      D3                           3 PIECES   Bureau        2\n", "7  198      D4                           3 PIECES   Bureau        2"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC C\n", " Nombre d'actifs: 8\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>184</td>\n", "      <td>MC1</td>\n", "      <td>RDC 36,35 m² /  MEZZANINE20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>185</td>\n", "      <td>MC2</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>186</td>\n", "      <td>MC3</td>\n", "      <td>RDC 36,35 m²/ MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>187</td>\n", "      <td>C1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>188</td>\n", "      <td>C2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>189</td>\n", "      <td>C3</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>190</td>\n", "      <td>C4</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>212</td>\n", "      <td>E4</td>\n", "      <td>Appartement 3 pieces</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle                        Description     Type  Type_ID\n", "0  184     MC1  RDC 36,35 m² /  MEZZANINE20,52 m²  Magasin        3\n", "1  185     MC2  RDC 36,35 m² / MEZZANINE 20,52 m²  Magasin        3\n", "2  186     MC3   RDC 36,35 m²/ MEZZANINE 20,52 m²  Magasin        3\n", "3  187      C1                           3 PIECES   Bureau        2\n", "4  188      C2                           3 PIECES   Bureau        2\n", "5  189      C3                           3 PIECES   Bureau        2\n", "6  190      C4                           3 PIECES   Bureau        2\n", "7  212      E4               Appartement 3 pieces   Bureau        2"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC B\n", " Nombre d'actifs: 8\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>176</td>\n", "      <td>MB1</td>\n", "      <td>RDC 36,35 m²/ MEZZANINE 20,52m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>177</td>\n", "      <td>MB2</td>\n", "      <td>RDC 36,35 m²/ MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>178</td>\n", "      <td>MB3</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>179</td>\n", "      <td>MB4</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>180</td>\n", "      <td>B1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>181</td>\n", "      <td>B2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>182</td>\n", "      <td>B3</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>183</td>\n", "      <td>B4</td>\n", "      <td>3 PIECES</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle                        Description     Type  Type_ID\n", "0  176     MB1    RDC 36,35 m²/ MEZZANINE 20,52m²  Magasin        3\n", "1  177     MB2   RDC 36,35 m²/ MEZZANINE 20,52 m²  Magasin        3\n", "2  178     MB3  RDC 36,35 m² / MEZZANINE 20,52 m²  Magasin        3\n", "3  179     MB4  RDC 36,35 m² / MEZZANINE 20,52 m²  Magasin        3\n", "4  180      B1                           3 PIECES   Bureau        2\n", "5  181      B2                           3 PIECES   Bureau        2\n", "6  182      B3                           3 PIECES   Bureau        2\n", "7  183      B4                           3 PIECES   Bureau        2"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIUM BLOC A\n", " Nombre d'actifs: 8\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>168</td>\n", "      <td>MA1</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>169</td>\n", "      <td>MA2</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>170</td>\n", "      <td>MA3</td>\n", "      <td>RDC 36,35  m²/ MEZZANINE 20,52 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>171</td>\n", "      <td>MA4</td>\n", "      <td>RDC 36,35 m² / MEZZANINE 20,36 m²</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>172</td>\n", "      <td>A1</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>173</td>\n", "      <td>A2</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>174</td>\n", "      <td>A3</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>175</td>\n", "      <td>A4</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle                        Description     Type  Type_ID\n", "0  168     MA1  RDC 36,35 m² / MEZZANINE 20,52 m²  Magasin        3\n", "1  169     MA2  RDC 36,35 m² / MEZZANINE 20,52 m²  Magasin        3\n", "2  170     MA3  RDC 36,35  m²/ MEZZANINE 20,52 m²  Magasin        3\n", "3  171     MA4  RDC 36,35 m² / MEZZANINE 20,36 m²  Magasin        3\n", "4  172      A1                        APPARTEMENT   Bureau        2\n", "5  173      A2                        APPARTEMENT   Bureau        2\n", "6  174      A3                        APPARTEMENT   Bureau        2\n", "7  175      A4                        APPARTEMENT   Bureau        2"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA LOT 14\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>167</td>\n", "      <td>LOT 14</td>\n", "      <td>Duplex 5 PIECES  sur 314 M² Symphonia</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle                            Description   Type  Type_ID\n", "0  167  LOT 14  Duplex 5 PIECES  sur 314 M² Symphonia  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LES CONCESSIONS DE BINGERVILLES\n", " Nombre d'actifs: 7\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>160</td>\n", "      <td>VILLA1</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>161</td>\n", "      <td>VILLA2</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>162</td>\n", "      <td>VILLA3</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>163</td>\n", "      <td>VILLA 4</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>164</td>\n", "      <td>VILLA5</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>165</td>\n", "      <td>VILLA6</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>166</td>\n", "      <td>VILLA7</td>\n", "      <td>4 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID  Libelle Description    Type  Type_ID\n", "0  160   VILLA1    4 PIECES  DUPLEX        5\n", "1  161   VILLA2    4 PIECES  DUPLEX        5\n", "2  162   VILLA3    4 PIECES  DUPLEX        5\n", "3  163  VILLA 4    4 PIECES  DUPLEX        5\n", "4  164   VILLA5    4 PIECES  DUPLEX        5\n", "5  165   VILLA6    4 PIECES  DUPLEX        5\n", "6  166   VILLA7    4 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 94\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>158</td>\n", "      <td>LOT 94</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  158  LOT 94    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 83 ILOT 09\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>159</td>\n", "      <td>LOT 83</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  159  LOT 83    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX LOT 48\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>155</td>\n", "      <td>Lot 48</td>\n", "      <td>None</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  155  Lot 48        None  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 21\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>154</td>\n", "      <td>VILLA</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  154   VILLA    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 85\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>153</td>\n", "      <td>charge locative</td>\n", "      <td>None</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID          Libelle Description   Type  Type_ID\n", "0  153  charge locative        None  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 107\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>152</td>\n", "      <td>VILLA</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  152   VILLA    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA DUPLEX LOT 37\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>151</td>\n", "      <td>villa</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  151   villa    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 101\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>150</td>\n", "      <td>R+1</td>\n", "      <td>None</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description             Type  Type_ID\n", "0  150     R+1        None  BLOC D'IMMEUBLE        8"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: LAURIERS 15 LOT 275\n", " Nombre d'actifs: 2\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>148</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>149</td>\n", "      <td>VILLA</td>\n", "      <td>4 PIECES</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID      Libelle Description         Type  Type_ID\n", "0  148  APPARTEMENT    3 PIECES  Appartement        1\n", "1  149        VILLA    4 PIECES        VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 68\n", " Nombre d'actifs: 2\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>145</td>\n", "      <td>APPARTEMENT 1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>146</td>\n", "      <td>MAGASIN 1</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID        Libelle Description         Type  Type_ID\n", "0  145  APPARTEMENT 1    3 PIECES  Appartement        1\n", "1  146      MAGASIN 1        None      Magasin        3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA IMMEUBLE LOT 59\n", " Nombre d'actifs: 3\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>141</td>\n", "      <td>CAVE</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>214</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>215</td>\n", "      <td>APPARTEMENT</td>\n", "      <td>None</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID      Libelle Description         Type  Type_ID\n", "0  141         CAVE        None      Magasin        3\n", "1  214  APPARTEMENT        None  Appartement        1\n", "2  215  APPARTEMENT        None       Bureau        2"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA  IMMEUBLE LOT 68\n", " Nombre d'actifs: 4\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>135</td>\n", "      <td>PROFESSIONNEL</td>\n", "      <td>None</td>\n", "      <td>APPARTEMENT A</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>144</td>\n", "      <td>PROFESSIONNEL</td>\n", "      <td>None</td>\n", "      <td>APPARTEMENT B</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>205</td>\n", "      <td>M68 1</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>206</td>\n", "      <td>M68 2</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID        Libelle Description           Type  Type_ID\n", "0  135  PROFESSIONNEL        None  APPARTEMENT A        6\n", "1  144  PROFESSIONNEL        None  APPARTEMENT B        7\n", "2  205          M68 1        <PERSON>        Ma<PERSON>        3\n", "3  206          M68 2        <PERSON>        Ma<PERSON>        3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 5 LOT 64\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>134</td>\n", "      <td>VILLA</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  134   VILLA    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 86\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>133</td>\n", "      <td>VILLA</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  133   VILLA    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX  LOT 97\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>132</td>\n", "      <td>Charges Locatives</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID            Libelle Description    Type  Type_ID\n", "0  132  Charges Locatives    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA DUPLEX 2 LOT 106\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>131</td>\n", "      <td>VILLA</td>\n", "      <td>5 PIECES</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  131   VILLA    5 PIECES  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: M'BADON VILLA\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>207</td>\n", "      <td>VILLA</td>\n", "      <td>None</td>\n", "      <td>DUPLEX</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description    Type  Type_ID\n", "0  207   VILLA        None  DUPLEX        5"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: SYMPHONIA VILLA LOT 79\n", " Nombre d'actifs: 1\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>120</td>\n", "      <td>LOT 79</td>\n", "      <td>None</td>\n", "      <td>VILLA</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description   Type  Type_ID\n", "0  120  LOT 79        None  VILLA        4"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE CISSE PORT BOUET\n", " Nombre d'actifs: 24\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>80</td>\n", "      <td>A1</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>81</td>\n", "      <td>A2</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>82</td>\n", "      <td>A3</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>83</td>\n", "      <td>A4</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>84</td>\n", "      <td>A5</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>85</td>\n", "      <td>A6</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>86</td>\n", "      <td>A7</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>87</td>\n", "      <td>B1</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>88</td>\n", "      <td>B2</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>89</td>\n", "      <td>B3</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>90</td>\n", "      <td>B4</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>91</td>\n", "      <td>B5</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>92</td>\n", "      <td>B6</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>93</td>\n", "      <td>B7</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>94</td>\n", "      <td>B8</td>\n", "      <td>STUDIO</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>95</td>\n", "      <td>C1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>96</td>\n", "      <td>C2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>97</td>\n", "      <td>C3</td>\n", "      <td>2 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>98</td>\n", "      <td>C4</td>\n", "      <td>2 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>99</td>\n", "      <td>D1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>100</td>\n", "      <td>D2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>101</td>\n", "      <td>D3</td>\n", "      <td>2 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>102</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>156</td>\n", "      <td>M</td>\n", "      <td>None</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ID Libelle Description         Type  Type_ID\n", "0    80      A1      STUDIO  Appartement        1\n", "1    81      A2      STUDIO  Appartement        1\n", "2    82      A3      STUDIO  Appartement        1\n", "3    83      A4      STUDIO  Appartement        1\n", "4    84      A5      STUDIO  Appartement        1\n", "5    85      A6      STUDIO  Appartement        1\n", "6    86      A7      STUDIO  Appartement        1\n", "7    87      B1      STUDIO  Appartement        1\n", "8    88      B2      STUDIO  Appartement        1\n", "9    89      B3      STUDIO  Appartement        1\n", "10   90      B4      STUDIO  Appartement        1\n", "11   91      B5      STUDIO  Appartement        1\n", "12   92      B6      STUDIO  Appartement        1\n", "13   93      B7      STUDIO  Appartement        1\n", "14   94      B8      STUDIO  Appartement        1\n", "15   95      C1    3 PIECES  Appartement        1\n", "16   96      C2    3 PIECES  Appartement        1\n", "17   97      C3    2 PIECES  Appartement        1\n", "18   98      C4    2 PIECES  Appartement        1\n", "19   99      D1    3 PIECES  Appartement        1\n", "20  100      D2    3 PIECES  Appartement        1\n", "21  101      D3    2 PIECES  Appartement        1\n", "22  102       M        None      Magasin        3\n", "23  156       M        None      Magasin        3"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: <PERSON>MM<PERSON>UBL<PERSON> CISSE JULES VERNE\n", " Nombre d'actifs: 8\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>70</td>\n", "      <td>A0</td>\n", "      <td>2 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>71</td>\n", "      <td>B0</td>\n", "      <td>2 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>73</td>\n", "      <td>A1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>75</td>\n", "      <td>B1</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>76</td>\n", "      <td>A2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>77</td>\n", "      <td>B2</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>103</td>\n", "      <td>A3</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>104</td>\n", "      <td>B3</td>\n", "      <td>3 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0   70      A0    2 PIECES  Appartement        1\n", "1   71      B0    2 PIECES  Appartement        1\n", "2   73      A1    3 PIECES  Appartement        1\n", "3   75      B1    3 PIECES  Appartement        1\n", "4   76      A2    3 PIECES  Appartement        1\n", "5   77      B2    3 PIECES  Appartement        1\n", "6  103      A3    3 PIECES  Appartement        1\n", "7  104      B3    3 PIECES  Appartement        1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: L'IMMEUBLE DE BIETRY SCI KAD\n", " Nombre d'actifs: 18\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>51</td>\n", "      <td>RC01</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>52</td>\n", "      <td>RC02</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>53</td>\n", "      <td>A101</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>54</td>\n", "      <td>A102</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>55</td>\n", "      <td>A103</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>56</td>\n", "      <td>A104</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>57</td>\n", "      <td>A202</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>58</td>\n", "      <td>A201</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>59</td>\n", "      <td>A203</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>60</td>\n", "      <td>A204</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>61</td>\n", "      <td>A301</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>62</td>\n", "      <td>A302</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>63</td>\n", "      <td>A303</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>64</td>\n", "      <td>A304</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>65</td>\n", "      <td>A401</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>66</td>\n", "      <td>A402</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>67</td>\n", "      <td>A403</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>68</td>\n", "      <td>A404</td>\n", "      <td>4 PIECES</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0   51    RC01    4 PIECES  Appartement        1\n", "1   52    RC02    4 PIECES  Appartement        1\n", "2   53    A101    4 PIECES  Appartement        1\n", "3   54    A102    4 PIECES  Appartement        1\n", "4   55    A103    4 PIECES  Appartement        1\n", "5   56    A104    4 PIECES  Appartement        1\n", "6   57    A202    4 PIECES  Appartement        1\n", "7   58    A201    4 PIECES  Appartement        1\n", "8   59    A203    4 PIECES  Appartement        1\n", "9   60    A204    4 PIECES  Appartement        1\n", "10  61    A301    4 PIECES  Appartement        1\n", "11  62    A302    4 PIECES  Appartement        1\n", "12  63    A303    4 PIECES  Appartement        1\n", "13  64    A304    4 PIECES  Appartement        1\n", "14  65    A401    4 PIECES  Appartement        1\n", "15  66    A402    4 PIECES  Appartement        1\n", "16  67    A403    4 PIECES  Appartement        1\n", "17  68    A404    4 PIECES  Appartement        1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: IMMEUBLE SCI KAD REMBLAIS\n", " Nombre d'actifs: 25\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9</td>\n", "      <td>SR1</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10</td>\n", "      <td>SR2</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11</td>\n", "      <td>SR3</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>12</td>\n", "      <td>S103</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>13</td>\n", "      <td>S104</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>14</td>\n", "      <td>S105</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>15</td>\n", "      <td>S106</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16</td>\n", "      <td>S207</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>17</td>\n", "      <td>S208</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>18</td>\n", "      <td>S209</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>19</td>\n", "      <td>S311</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>20</td>\n", "      <td>S310</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>21</td>\n", "      <td>S312</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>22</td>\n", "      <td>S413</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>23</td>\n", "      <td>S414</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>24</td>\n", "      <td>S415</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>25</td>\n", "      <td>A216</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>26</td>\n", "      <td>A227</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>27</td>\n", "      <td>A238</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>28</td>\n", "      <td>A249</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>29</td>\n", "      <td>A301</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>31</td>\n", "      <td>A312</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>32</td>\n", "      <td>A323</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>33</td>\n", "      <td>A334</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>50</td>\n", "      <td>A345</td>\n", "      <td>None</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ID Libelle Description         Type  Type_ID\n", "0    9     SR1        None  Appartement        1\n", "1   10     SR2        None  Appartement        1\n", "2   11     SR3        None  Appartement        1\n", "3   12    S103        None  Appartement        1\n", "4   13    S104        None  Appartement        1\n", "5   14    S105        None  Appartement        1\n", "6   15    S106        None  Appartement        1\n", "7   16    S207        None  Appartement        1\n", "8   17    S208        None  Appartement        1\n", "9   18    S209        None  Appartement        1\n", "10  19    S311        None  Appartement        1\n", "11  20    S310        None  Appartement        1\n", "12  21    S312        None  Appartement        1\n", "13  22    S413        None  Appartement        1\n", "14  23    S414        None  Appartement        1\n", "15  24    S415        None  Appartement        1\n", "16  25    A216        None  Appartement        1\n", "17  26    A227        None  Appartement        1\n", "18  27    A238        None  Appartement        1\n", "19  28    A249        None  Appartement        1\n", "20  29    A301        None  Appartement        1\n", "21  31    A312        None  Appartement        1\n", "22  32    A323        None  Appartement        1\n", "23  33    A334        None  Appartement        1\n", "24  50    A345        None  Appartement        1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", " BIEN: EGDGDG\n", " Nombre d'actifs: 4\n", " Liste des actifs:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Description</th>\n", "      <th>Type</th>\n", "      <th>Type_ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>AB-01</td>\n", "      <td>Appartement AB du niveau 1</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>AB-2</td>\n", "      <td>Appartement AB 2 du niveau 1</td>\n", "      <td>Appartement</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>AB_B-01</td>\n", "      <td>Bureau AB_B du niveau 1</td>\n", "      <td>Bureau</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>AB_M-01</td>\n", "      <td>Magasin AB_M du niveau 1</td>\n", "      <td>Magasin</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID  Libelle                   Description         Type  Type_ID\n", "0   1    AB-01    Appartement AB du niveau 1  Appartement        1\n", "1   2     AB-2  Appartement AB 2 du niveau 1  Appartement        1\n", "2   3  AB_B-01       Bureau AB_B du niveau 1       Bureau        2\n", "3   4  AB_M-01      Magasin AB_M du niveau 1      Magasin        3"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for _, bien_row in df_clean.iterrows():\n", "    bien_nom = bien_row['Bien']\n", "    analyser_actifs(bien_nom, df_actifs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}