#!/usr/bin/env python
# coding: utf-8

# # ANALYSE DES DONNÉES DE GESTION LOCATIVE
# ## Liste des imports utiles

import pandas as pd
import json
import ast
import re
from datetime import datetime

# # Chargement du fichier CSV

csv_file = 'G_locative.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

# # Nettoyage et préparation des données

# ## Conversion des chaînes JSON en objets Python

def safe_literal_eval(s):
    """Convertit une chaîne en objet Python de manière sécurisée"""
    if pd.isna(s) or s == '[]' or s == '' or s == 'None':
        return []
    try:
        # Nettoyer la chaîne pour gérer les guillemets simples
        s = s.replace("'", '"')
        # Remplacer les valeurs booléennes non entre guillemets
        s = re.sub(r':\s*None\b', ': null', s)
        s = re.sub(r':\s*True\b', ': true', s)
        s = re.sub(r':\s*False\b', ': false', s)
        return json.loads(s)
    except (ValueError, json.JSONDecodeError, SyntaxError) as e:
        print(f"Erreur de conversion pour la valeur: {s}")
        print(f"Erreur: {e}")
        return []

# Conversion des colonnes contenant des données JSON
json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']

# Créer une copie pour éviter les avertissements
print("\nConversion des données JSON...")
df = df_raw.copy()

# Convertir les colonnes JSON
for col in json_columns:
    if col in df.columns:
        print(f"Traitement de la colonne: {col}")
        df[col] = df[col].fillna('[]').apply(safe_literal_eval)
    else:
        print(f"Colonne {col} non trouvée dans le DataFrame")

# Afficher les informations sur le DataFrame
print("\nStructure du DataFrame après conversion:")
df.info()

# ## Nettoyage des données

class Cleaning:
    def __init__(self, df):
        self.df = df.copy()
    
    def clean_column_names(self):
        """Nettoie les noms des colonnes"""
        # Renommer 'libelle' en 'Bien' et capitaliser la première lettre des autres colonnes
        self.df = self.df.rename(columns={'libelle': 'Bien'})
        self.df.columns = [col.capitalize() if col != 'Bien' else col for col in self.df.columns]
        return self
    
    def drop_columns(self, columns_to_drop):
        """Supprime les colonnes spécifiées"""
        self.df = self.df.drop(columns=columns_to_drop, errors='ignore')
        return self
    
    def clean_data(self):
        """Nettoie les données"""
        # Convertir les dates
        if 'Created_at' in self.df.columns:
            self.df['Created_at'] = pd.to_datetime(self.df['Created_at'])
        
        # Nettoyer les montants numériques
        numeric_cols = ['Totalcharges', 'Totaloyer', 'Totalimpayer']
        for col in numeric_cols:
            if col in self.df.columns:
                # Supprimer les espaces et convertir en numérique
                self.df[col] = pd.to_numeric(self.df[col].astype(str).str.replace(' ', ''), errors='coerce')
        
        return self
    
    def get_cleaned_data(self):
        """Retourne le DataFrame nettoyé"""
        return self.df

# Appliquer le nettoyage
print("\nNettoyage des données...")
cleaner = Cleaning(df)
df_clean = (cleaner
            .drop_columns(['id'])  # Supprimer la colonne 'id' comme demandé
            .clean_column_names()  # Renommer et capitaliser
            .clean_data()          # Nettoyer les données
            .get_cleaned_data()    # Récupérer le DataFrame nettoyé
           )

# Afficher le résultat
print("\nAperçu des données nettoyées:")
display(df_clean.head())

# # Fonctions d'analyse et d'éclatement des données

def extract_proprietaires(df):
    """Extrait et formate les données des propriétaires"""
    proprietaires_list = []
    
    for _, row in df.iterrows():
        bien = row['Bien']
        proprietaires = row.get('Proprietaires', [])
        
        if not isinstance(proprietaires, list):
            continue
            
        for prop in proprietaires:
            if not isinstance(prop, dict):
                continue
                
            user = prop.get('user', {}) if isinstance(prop.get('user'), dict) else {}
            
            proprietaires_list.append({
                'Bien': bien,
                'Proprietaire_id': prop.get('user_id'),
                'Nom': user.get('name'),
                'Email': user.get('email'),
                'Type_user': prop.get('type_user'),
                'Date_creation': prop.get('created_at')
            })
    
    return pd.DataFrame(proprietaires_list)

def extract_charges(df):
    """Extrait et formate les données des charges"""
    charges_list = []
    
    for _, row in df.iterrows():
        bien = row['Bien']
        charges = row.get('Charges', [])
        
        if not isinstance(charges, list):
            charges = [charges] if pd.notna(charges) else []
            
        for charge in charges:
            if not isinstance(charge, dict):
                continue
                
            charges_list.append({
                'Bien': bien,
                'Charge_id': charge.get('id'),
                'Libelle': charge.get('libelle'),
                'Montant': charge.get('montant')
            })
    
    return pd.DataFrame(charges_list)

def extract_actifs(df):
    """Extrait et formate les données des actifs"""
    actifs_list = []
    
    for _, row in df.iterrows():
        bien = row['Bien']
        actifs = row.get('Actifs', [])
        
        if not isinstance(actifs, list):
            actifs = [actifs] if pd.notna(actifs) else []
            
        for actif in actifs:
            if not isinstance(actif, dict):
                continue
                
            actif_type = actif.get('type', {}) if isinstance(actif.get('type'), dict) else {}
            
            actifs_list.append({
                'Bien': bien,
                'Actif_id': actif.get('id'),
                'Libelle': actif.get('libelle'),
                'Description': actif.get('description'),
                'Type_actif': actif_type.get('libelle')
            })
    
    return pd.DataFrame(actifs_list)

# Extraire les données
df_proprietaires = extract_proprietaires(df_clean)
df_charges = extract_charges(df_clean)
df_actifs = extract_actifs(df_clean)

# Afficher les données extraites
print("\nAperçu des propriétaires:")
display(df_proprietaires.head())

print("\nAperçu des charges:")
display(df_charges.head())

print("\nAperçu des actifs:")
display(df_actifs.head())

# # Analyse par bien

def analyser_bien(nom_bien, df_clean, df_proprietaires, df_charges, df_actifs):
    """Analyse complète d'un bien immobilier"""
    print(f"\n{'='*80}")
    print(f"ANALYSE DU BIEN: {nom_bien}")
    print(f"{'='*80}")
    
    # Informations de base
    bien = df_clean[df_clean['Bien'] == nom_bien].iloc[0]
    
    print(f"\nINFORMATIONS GÉNÉRALES")
    print(f"Adresse: {bien.get('Adresse', 'Non spécifiée')}")
    print(f"Type: {bien.get('Type', {}).get('libelle', 'Non spécifié')}")
    print(f"Date de création: {bien.get('Created_at', 'Non spécifiée')}")
    print(f"Total des charges: {bien.get('Totalcharges', 0):,.0f} FCFA")
    print(f"Total des loyers: {bien.get('Totaloyer', 0):,.0f} FCFA")
    print(f"Total impayé: {bien.get('Totalimpayer', 0):,.0f} FCFA")
    
    # Propriétaires
    proprietaires = df_proprietaires[df_proprietaires['Bien'] == nom_bien]
    print(f"\nPROPRIÉTAIRES ({len(proprietaires)})")
    if not proprietaires.empty:
        display(proprietaires[['Nom', 'Email', 'Type_user']])
    else:
        print("Aucun propriétaire trouvé")
    
    # Charges
    charges = df_charges[df_charges['Bien'] == nom_bien]
    print(f"\nCHARGES ({len(charges)})")
    if not charges.empty:
        display(charges[['Libelle', 'Montant']])
    else:
        print("Aucune charge trouvée")
    
    # Actifs
    actifs = df_actifs[df_actifs['Bien'] == nom_bien]
    print(f"\nACTIFS ({len(actifs)})")
    if not actifs.empty:
        display(actifs[['Libelle', 'Description', 'Type_actif']])
    else:
        print("Aucun actif trouvé")

# Exécuter l'analyse pour chaque bien
for bien in df_clean['Bien'].unique():
    analyser_bien(bien, df_clean, df_proprietaires, df_charges, df_actifs)

# # Statistiques globales

print("\n" + "="*80)
print("STATISTIQUES GLOBALES")
print("="*80)

# Nombre total de biens
print(f"\nNombre total de biens: {len(df_clean)}")

# Répartition par type de bien
if 'Type' in df_clean.columns:
    types_biens = df_clean['Type'].apply(lambda x: x.get('libelle') if isinstance(x, dict) and 'libelle' in x else 'Non spécifié')
    print("\nRÉPARTITION PAR TYPE DE BIEN:")
    print(types_biens.value_counts().to_string())

# Montant total des loyers
total_loyers = df_clean['Totaloyer'].sum()
print(f"\nMontant total des loyers: {total_loyers:,.0f} FCFA")

# Montant total des charges
total_charges = df_clean['Totalcharges'].sum()
print(f"Montant total des charges: {total_charges:,.0f} FCFA")

# Montant total des impayés
total_impayes = df_clean['Totalimpayer'].sum()
print(f"Montant total des impayés: {total_impayes:,.0f} FCFA")

# Taux de recouvrement
taux_recouvrement = ((total_loyers - total_impayes) / total_loyers * 100) if total_loyers > 0 else 0
print(f"Taux de recouvrement: {taux_recouvrement:.1f}%")

# Top 5 des biens par loyer
print("\nTOP 5 DES BIENS PAR LOYER:")
top_biens = df_clean.sort_values('Totaloyer', ascending=False).head()
for i, (_, bien) in enumerate(top_biens.iterrows(), 1):
    print(f"{i}. {bien['Bien']}: {bien['Totaloyer']:,.0f} FCFA")

# # Sauvegarde des données traitées

# Créer un dictionnaire avec toutes les données traitées
data_processed = {
    'biens': df_clean,
    'proprietaires': df_proprietaires,
    'charges': df_charges,
    'actifs': df_actifs
}

# Sauvegarder dans un fichier Excel avec plusieurs onglets
output_file = 'gestion_locative_analyse.xlsx'
with pd.ExcelWriter(output_file) as writer:
    df_clean.to_excel(writer, sheet_name='Biens', index=False)
    df_proprietaires.to_excel(writer, sheet_name='Propriétaires', index=False)
    df_charges.to_excel(writer, sheet_name='Charges', index=False)
    df_actifs.to_excel(writer, sheet_name='Actifs', index=False)

print(f"\nAnalyse terminée. Les données ont été enregistrées dans {output_file}")
