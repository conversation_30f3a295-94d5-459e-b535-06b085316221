import pandas as pd
import json
import ast

# 1. Chargement des données JSON
json_file = 'gstockapprovisionnement_data.json'

with open(json_file, 'r', encoding='utf-8') as f:
    json_data = json.load(f)

print(f"Données JSON chargées depuis {json_file}")

# 2. Conversion JSON vers DataFrame
if isinstance(json_data, list):
    df = pd.DataFrame(json_data)
else:
    df = pd.DataFrame([json_data])

print(f"DataFrame créé: {df.shape}")
df.head()

df

# 3. Sauvegarde en CSV
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
csv_filename = f'gstockapprovisionnement_{timestamp}.csv'

os.makedirs('../../exports', exist_ok=True)
csv_filepath = f'../../exports/{csv_filename}'

df.to_csv(csv_filepath, index=False, encoding='utf-8')
print(f"CSV sauvegardé: {csv_filepath}")
print(f"Nombre de lignes: {len(df)}")

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
df_loaded.head()

# 5. Visualisations avec Plotly
# Graphique simple
fig1 = px.bar(df_loaded, x='title', title='Stock Approvisionnement')
fig1.show()

# Graphique en secteurs
fig2 = px.pie(df_loaded, names='title', title='Répartition des données')
fig2.show()

# 6. Prêt pour les analyses
print("\nDonnées prêtes pour l'analyse!")
print(f"DataFrame disponible: df_loaded")
print(f"Fichier CSV: {csv_filepath}")

