{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'gstockapprovisionnement_data.json'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 1. Chargement des données JSON\u001b[39;00m\n\u001b[32m      2\u001b[39m json_file = \u001b[33m'\u001b[39m\u001b[33mgstockapprovisionnement_data.json\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(json_file, \u001b[33m'\u001b[39m\u001b[33mr\u001b[39m\u001b[33m'\u001b[39m, encoding=\u001b[33m'\u001b[39m\u001b[33mutf-8\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m      5\u001b[39m     json_data = json.load(f)\n\u001b[32m      7\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mDonnées JSON chargées depuis \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mjson_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/IPython/core/interactiveshell.py:326\u001b[39m, in \u001b[36m_modified_open\u001b[39m\u001b[34m(file, *args, **kwargs)\u001b[39m\n\u001b[32m    319\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[32m0\u001b[39m, \u001b[32m1\u001b[39m, \u001b[32m2\u001b[39m}:\n\u001b[32m    320\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    321\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mIPython won\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m by default \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    322\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    323\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33my<PERSON> can use builtins\u001b[39m\u001b[33m'\u001b[39m\u001b[33m open.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    324\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m326\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m io_open(file, *args, **kwargs)\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'gstockapprovisionnement_data.json'"]}], "source": ["# 1. Chargement des données JSON\n", "json_file = 'gstockapprovisionnement_data.json'\n", "\n", "with open(json_file, 'r', encoding='utf-8') as f:\n", "    json_data = json.load(f)\n", "\n", "print(f\"Données JSON chargées depuis {json_file}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame créé: (1, 1)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stocks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'programme': {'id': 7, 'libelle': 'K2', 'nom...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              stocks\n", "0  [{'programme': {'id': 7, 'libelle': 'K2', 'nom..."]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. Conversion JSON vers DataFrame\n", "if isinstance(json_data, list):\n", "    df = pd.DataFrame(json_data)\n", "else:\n", "    df = pd.DataFrame([json_data])\n", "\n", "print(f\"DataFrame créé: {df.shape}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m df\n", "\u001b[31mNameError\u001b[39m: name 'df' is not defined"]}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV sauvegardé: ../../exports/gstockapprovisionnement_20250715_100232.csv\n", "Nombre de lignes: 1\n"]}], "source": ["# 3. <PERSON><PERSON><PERSON><PERSON> en CSV\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'gstockapprovisionnement_{timestamp}.csv'\n", "\n", "os.makedirs('../../exports', exist_ok=True)\n", "csv_filepath = f'../../exports/{csv_filename}'\n", "\n", "df.to_csv(csv_filepath, index=False, encoding='utf-8')\n", "print(f\"CSV sauvegardé: {csv_filepath}\")\n", "print(f\"Nombre de lignes: {len(df)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV rechargé: (1, 1)\n", "Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stocks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'programme': {'id': 7, 'libelle': 'K2', 'nom...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              stocks\n", "0  [{'programme': {'id': 7, 'libelle': 'K2', 'nom..."]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Lecture du CSV sauvegardé\n", "df_loaded = pd.read_csv(csv_filepath)\n", "\n", "print(f\"CSV rechargé: {df_loaded.shape}\")\n", "print(\"Aperçu des données:\")\n", "df_loaded.head()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'px' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 5. Visualisations avec Plotly\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# Graphique simple\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m fig1 = px.bar(df_loaded, x=\u001b[33m'\u001b[39m\u001b[33mtitle\u001b[39m\u001b[33m'\u001b[39m, title=\u001b[33m'\u001b[39m\u001b[33mStock Approvisionnement\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      4\u001b[39m fig1.show()\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# Graphique en secteurs\u001b[39;00m\n", "\u001b[31mNameError\u001b[39m: name 'px' is not defined"]}], "source": ["# 5. Visualisations avec Plotly\n", "# Graphique simple\n", "fig1 = px.bar(df_loaded, x='title', title='Stock Approvisionnement')\n", "fig1.show()\n", "\n", "# Graphique en secteurs\n", "fig2 = px.pie(df_loaded, names='title', title='Répartition des données')\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Données prêtes pour l'analyse!\n", "DataFrame disponible: df_loaded\n", "Fichier CSV: ../../exports/gstockapprovisionnement_20250714_121000.csv\n"]}], "source": ["# 6. <PERSON><PERSON><PERSON><PERSON> pour les analyses\n", "print(\"\\nDonnées prêtes pour l'analyse!\")\n", "print(f\"DataFrame disponible: df_loaded\")\n", "print(f\"Fichier CSV: {csv_filepath}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}