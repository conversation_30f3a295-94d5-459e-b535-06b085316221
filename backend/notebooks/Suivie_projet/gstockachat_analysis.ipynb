import pandas as pd
import json
import ast

csv_file = 'gstockachat_data.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

def parse_json_data(json_str):
    parsed = ast.literal_eval(json_str)
    print("Parsing réussi")
    return parsed

# Parsing des données
parsed

# 3. Sauvegarde en CSV
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
csv_filename = f'gstockachat_{timestamp}.csv'

os.makedirs('../../exports', exist_ok=True)
csv_filepath = f'../../exports/{csv_filename}'

df.to_csv(csv_filepath, index=False, encoding='utf-8')
print(f"CSV sauvegardé: {csv_filepath}")
print(f"Nombre de lignes: {len(df)}")

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
df_loaded.head()

# 5. Visualisations avec Plotly
# Graphique simple
fig1 = px.bar(df_loaded, x='title', title='Stock Achats')
fig1.show()

# Graphique en secteurs
fig2 = px.pie(df_loaded, names='title', title='Répartition des données')
fig2.show()

# 6. Prêt pour les analyses
print("\nDonnées prêtes pour l'analyse!")
print(f"DataFrame disponible: df_loaded")
print(f"Fichier CSV: {csv_filepath}")

