{"cells": [{"cell_type": "markdown", "id": "3814e2f3", "metadata": {}, "source": ["# Analyse des données DQE (Devis Quantitatif Estimatif)\n", "\n", "Ce notebook analyse les données DQE qui contiennent des informations sur différents projets de construction, y compris les détails des coûts et des éléments."]}, {"cell_type": "code", "execution_count": 5, "id": "5c3979e6", "metadata": {}, "outputs": [], "source": ["# Import des bibliothèques nécessaires\n", "import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 6, "id": "62a33002", "metadata": {}, "outputs": [{"ename": "JSONDecodeError", "evalue": "Expecting property name enclosed in double quotes: line 1 column 2 (char 1)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mJSONDecodeError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Lecture du fichier CSV et conversion de la colonne 'data'\u001b[39;00m\n\u001b[32m      2\u001b[39m df = pd.read_csv(\u001b[33m'\u001b[39m\u001b[33mdqedata_data.csv\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m df[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m] = df[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m].apply(json.loads)\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# Affichage des données de base\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m=== Données de base ===\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/series.py:4924\u001b[39m, in \u001b[36mSeries.apply\u001b[39m\u001b[34m(self, func, convert_dtype, args, by_row, **kwargs)\u001b[39m\n\u001b[32m   4789\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mapply\u001b[39m(\n\u001b[32m   4790\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   4791\u001b[39m     func: AggFuncType,\n\u001b[32m   (...)\u001b[39m\u001b[32m   4796\u001b[39m     **kwargs,\n\u001b[32m   4797\u001b[39m ) -> DataFrame | Series:\n\u001b[32m   4798\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   4799\u001b[39m \u001b[33;03m    Invoke function on values of Series.\u001b[39;00m\n\u001b[32m   4800\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   4915\u001b[39m \u001b[33;03m    dtype: float64\u001b[39;00m\n\u001b[32m   4916\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m   4917\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m SeriesApply(\n\u001b[32m   4918\u001b[39m         \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   4919\u001b[39m         func,\n\u001b[32m   4920\u001b[39m         convert_dtype=convert_dtype,\n\u001b[32m   4921\u001b[39m         by_row=by_row,\n\u001b[32m   4922\u001b[39m         args=args,\n\u001b[32m   4923\u001b[39m         kwargs=kwargs,\n\u001b[32m-> \u001b[39m\u001b[32m4924\u001b[39m     ).apply()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/apply.py:1427\u001b[39m, in \u001b[36mSeriesApply.apply\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1424\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_compat()\n\u001b[32m   1426\u001b[39m \u001b[38;5;66;03m# self.func is Callable\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1427\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_standard()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/apply.py:1507\u001b[39m, in \u001b[36mSeriesApply.apply_standard\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1501\u001b[39m \u001b[38;5;66;03m# row-wise access\u001b[39;00m\n\u001b[32m   1502\u001b[39m \u001b[38;5;66;03m# apply doesn't have a `na_action` keyword and for backward compat reasons\u001b[39;00m\n\u001b[32m   1503\u001b[39m \u001b[38;5;66;03m# we need to give `na_action=\"ignore\"` for categorical data.\u001b[39;00m\n\u001b[32m   1504\u001b[39m \u001b[38;5;66;03m# TODO: remove the `na_action=\"ignore\"` when that default has been changed in\u001b[39;00m\n\u001b[32m   1505\u001b[39m \u001b[38;5;66;03m#  Categorical (GH51645).\u001b[39;00m\n\u001b[32m   1506\u001b[39m action = \u001b[33m\"\u001b[39m\u001b[33mignore\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(obj.dtype, CategoricalDtype) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1507\u001b[39m mapped = obj._map_values(\n\u001b[32m   1508\u001b[39m     mapper=curried, na_action=action, convert=\u001b[38;5;28mself\u001b[39m.convert_dtype\n\u001b[32m   1509\u001b[39m )\n\u001b[32m   1511\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(mapped) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(mapped[\u001b[32m0\u001b[39m], ABCSeries):\n\u001b[32m   1512\u001b[39m     \u001b[38;5;66;03m# GH#43986 Need to do list(mapped) in order to get treated as nested\u001b[39;00m\n\u001b[32m   1513\u001b[39m     \u001b[38;5;66;03m#  See also GH#25959 regarding EA support\u001b[39;00m\n\u001b[32m   1514\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m obj._constructor_expanddim(\u001b[38;5;28mlist\u001b[39m(mapped), index=obj.index)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/base.py:921\u001b[39m, in \u001b[36mIndexOpsMixin._map_values\u001b[39m\u001b[34m(self, mapper, na_action, convert)\u001b[39m\n\u001b[32m    918\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(arr, ExtensionArray):\n\u001b[32m    919\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m arr.map(mapper, na_action=na_action)\n\u001b[32m--> \u001b[39m\u001b[32m921\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m algorithms.map_array(arr, mapper, na_action=na_action, convert=convert)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/algorithms.py:1743\u001b[39m, in \u001b[36mmap_array\u001b[39m\u001b[34m(arr, mapper, na_action, convert)\u001b[39m\n\u001b[32m   1741\u001b[39m values = arr.astype(\u001b[38;5;28mobject\u001b[39m, copy=\u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m)\n\u001b[32m   1742\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m na_action \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1743\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m lib.map_infer(values, mapper, convert=convert)\n\u001b[32m   1744\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1745\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m lib.map_infer_mask(\n\u001b[32m   1746\u001b[39m         values, mapper, mask=isna(values).view(np.uint8), convert=convert\n\u001b[32m   1747\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mlib.pyx:2972\u001b[39m, in \u001b[36mpandas._libs.lib.map_infer\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/json/__init__.py:346\u001b[39m, in \u001b[36mloads\u001b[39m\u001b[34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[39m\n\u001b[32m    341\u001b[39m     s = s.decode(detect_encoding(s), \u001b[33m'\u001b[39m\u001b[33msurrogatepass\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    343\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[32m    344\u001b[39m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[32m    345\u001b[39m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[32m--> \u001b[39m\u001b[32m346\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m _default_decoder.decode(s)\n\u001b[32m    347\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    348\u001b[39m     \u001b[38;5;28mcls\u001b[39m = JSONDecoder\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/json/decoder.py:345\u001b[39m, in \u001b[36mJSONDecoder.decode\u001b[39m\u001b[34m(self, s, _w)\u001b[39m\n\u001b[32m    340\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mdecode\u001b[39m(\u001b[38;5;28mself\u001b[39m, s, _w=WHITESPACE.match):\n\u001b[32m    341\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[32m    342\u001b[39m \u001b[33;03m    containing a JSON document).\u001b[39;00m\n\u001b[32m    343\u001b[39m \n\u001b[32m    344\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m345\u001b[39m     obj, end = \u001b[38;5;28mself\u001b[39m.raw_decode(s, idx=_w(s, \u001b[32m0\u001b[39m).end())\n\u001b[32m    346\u001b[39m     end = _w(s, end).end()\n\u001b[32m    347\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m end != \u001b[38;5;28mlen\u001b[39m(s):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/json/decoder.py:361\u001b[39m, in \u001b[36mJSONDecoder.raw_decode\u001b[39m\u001b[34m(self, s, idx)\u001b[39m\n\u001b[32m    352\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Decode a JSON document from ``s`` (a ``str`` beginning with\u001b[39;00m\n\u001b[32m    353\u001b[39m \u001b[33;03ma JSON document) and return a 2-tuple of the Python\u001b[39;00m\n\u001b[32m    354\u001b[39m \u001b[33;03mrepresentation and the index in ``s`` where the document ended.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    358\u001b[39m \n\u001b[32m    359\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    360\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m361\u001b[39m     obj, end = \u001b[38;5;28mself\u001b[39m.scan_once(s, idx)\n\u001b[32m    362\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m    363\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[33m\"\u001b[39m\u001b[33mExpecting value\u001b[39m\u001b[33m\"\u001b[39m, s, err.value) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[31mJSONDecodeError\u001b[39m: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)"]}], "source": ["# Lecture du fichier CSV et conversion de la colonne 'data'\n", "df = pd.read_csv('dqedata_data.csv')\n", "df['data'] = df['data'].apply(json.loads)\n", "\n", "# Affichage des données de base\n", "print(\"=== Données de base ===\")\n", "for index, row in df.iterrows():\n", "    print(f\"\\nProjet {index + 1}:\")\n", "    print(f\"Titre: {row['title']}\")\n", "    print(f\"Description: {row['description']}\")\n", "    print(f\"Date de création: {row['created_at']}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "2550fe85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Informations détaillées des projets ===\n"]}, {"ename": "TypeError", "evalue": "string indices must be integers, not 'str'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m index, row \u001b[38;5;129;01min\u001b[39;00m df.iterrows():\n\u001b[32m      4\u001b[39m     data = row[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mProjet: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mproject_name\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCoût total: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mtotal_cost\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mcurrency\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      7\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mStatut: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mstatus\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mTypeError\u001b[39m: string indices must be integers, not 'str'"]}], "source": ["# Affichage des informations détaillées des projets\n", "print(\"\\n=== Informations détaillées des projets ===\")\n", "for index, row in df.iterrows():\n", "    data = row['data']\n", "    print(f\"\\nProjet: {data['project_name']}\")\n", "    print(f\"Coût total: {data['total_cost']:,} {data['currency']}\")\n", "    print(f\"Statut: {data['status']}\")\n", "    print(f\"Créé par: {data['created_by']}\")\n", "    print(f\"Date de validation: {data['validation_date']}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "f83665c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Items par projet ===\n"]}, {"ename": "TypeError", "evalue": "string indices must be integers, not 'str'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m index, row \u001b[38;5;129;01min\u001b[39;00m df.iterrows():\n\u001b[32m      4\u001b[39m     data = row[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mItems du projet: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mproject_name\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\"\u001b[39m * \u001b[32m100\u001b[39m)\n\u001b[32m      7\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mCatégorie\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m<15\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m | \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mDescription\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m<30\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m | \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mQuantité\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m<8\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m | \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mUnité\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m<8\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m | \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mPrix unitaire\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m>12\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m | \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33mPrix total\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m>12\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mTypeError\u001b[39m: string indices must be integers, not 'str'"]}], "source": ["# Affichage des items de chaque projet\n", "print(\"\\n=== Items par projet ===\")\n", "for index, row in df.iterrows():\n", "    data = row['data']\n", "    print(f\"\\nItems du projet: {data['project_name']}\")\n", "    print(\"-\" * 100)\n", "    print(f\"{'Catégorie':<15} | {'Description':<30} | {'Quantité':<8} | {'Unité':<8} | {'Prix unitaire':>12} | {'Prix total':>12}\")\n", "    print(\"-\" * 100)\n", "    \n", "    for item in data['items']:\n", "        print(f\"{item['category']:<15} | {item['description']:<30} | {item['quantity']:<8} | {item['unit']:<8} | {item['unit_price']:>12,} | {item['total_price']:>12,}\")\n", "    print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": 9, "id": "599680f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Résumé des coûts ===\n"]}, {"ename": "TypeError", "evalue": "string indices must be integers, not 'str'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m index, row \u001b[38;5;129;01min\u001b[39;00m df.iterrows():\n\u001b[32m      4\u001b[39m     data = row[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mProjet: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[33m'\u001b[39m\u001b[33mproject_name\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mCoûts par catégorie:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      8\u001b[39m     \u001b[38;5;66;03m# <PERSON><PERSON><PERSON> les coûts par catégorie\u001b[39;00m\n", "\u001b[31mTypeError\u001b[39m: string indices must be integers, not 'str'"]}], "source": ["# Analyse des coûts\n", "print(\"\\n=== Résumé des coûts ===\")\n", "for index, row in df.iterrows():\n", "    data = row['data']\n", "    print(f\"\\nProjet: {data['project_name']}\")\n", "    print(\"Coûts par catégorie:\")\n", "    \n", "    # Calculer les coûts par catégorie\n", "    categories = {}\n", "    for item in data['items']:\n", "        cat = item['category']\n", "        if cat not in categories:\n", "            categories[cat] = 0\n", "        categories[cat] += item['total_price']\n", "    \n", "    # Affiche<PERSON> les coûts par catégorie\n", "    for cat, cout in categories.items():\n", "        print(f\"{cat:<20}: {cout:>15,} {data['currency']}\")\n", "    print(f\"{'TOTAL':<20}: {data['total_cost']:>15,} {data['currency']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "96feb15a-f729-419c-bc01-5dbfaf8f5f9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}