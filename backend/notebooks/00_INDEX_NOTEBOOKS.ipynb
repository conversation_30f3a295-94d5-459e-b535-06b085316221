{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## CONVERSION AUTOMATIQUE DE TOUS LES FICHIERS JSON\n", "\n", "Ce notebook contient les cellules de conversion pour transformer tous les fichiers JSON en CSV et les placer dans les dossiers correspondants aux notebooks.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CONVERSION JSON VERS CSV - KAYDAN ANALYTICS HUB\n", "==================================================\n", "Début de la conversion: 2025-07-21 15:10:15\n", "\n"]}], "source": ["# Imports nécessaires pour toutes les conversions\n", "import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "\n", "def convert_json_to_csv(json_file_path, csv_output_path, dataset_name):\n", "    \"\"\"Fonction utilitaire pour convertir JSON en CSV\"\"\"\n", "    try:\n", "        # Charger le fichier JSON\n", "        with open(json_file_path, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "        \n", "        # Convertir en DataFrame\n", "        if isinstance(data, list):\n", "            df = pd.DataFrame(data)\n", "        elif isinstance(data, dict):\n", "            df = pd.DataFrame([data])\n", "        else:\n", "            df = pd.DataFrame(data)\n", "        \n", "        # <PERSON><PERSON><PERSON> le dossier de destination s'il n'existe pas\n", "        os.makedirs(os.path.dirname(csv_output_path), exist_ok=True)\n", "        \n", "        # Sauvegarder en CSV\n", "        df.to_csv(csv_output_path, index=False, encoding='utf-8')\n", "        \n", "        print(f\"✓ {dataset_name}: {len(df)} lignes converties\")\n", "        print(f\"  JSON: {json_file_path}\")\n", "        print(f\"  CSV:  {csv_output_path}\")\n", "        print()\n", "        \n", "        return True\n", "    except Exception as e:\n", "        print(f\"✗ Erreur pour {dataset_name}: {str(e)}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ UserProfile Data: 3 lignes converties\n", "  JSON: ./Json/userprofile_data.json\n", "  CSV:  ./userprofile_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# 1. CONVERSION - UserProfile Analysis\n", "convert_json_to_csv(\n", "    json_file_path='./Json/userprofile_data.json',  # Ajout du ./\n", "    csv_output_path='./userprofile_data.csv',       # Chemin relatif explicite\n", "    dataset_name='UserProfile Data'\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ DQE Data: 2 lignes converties\n", "  JSON: Json/dqedata_data.json\n", "  CSV:  Creation_definition_projet/dqedata_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. CONVERSION - DQE Data Analysis\n", "convert_json_to_csv(\n", "    json_file_path='Json/dqedata_data.json',\n", "    csv_output_path='Creation_definition_projet/dqedata_data.csv',\n", "    dataset_name='DQE Data'\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ École des Talents: 3 lignes converties\n", "  JSON: Json/ecole_talents_data.json\n", "  CSV:  <PERSON><PERSON><PERSON>_projet/ecole_talents_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3. CONVERSION - École des Talents\n", "convert_json_to_csv(\n", "    json_file_path='Json/ecole_talents_data.json',\n", "    csv_output_path='<PERSON><PERSON><PERSON>_projet/ecole_talents_data.csv',\n", "    dataset_name='École des Talents'\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ G-Projet Data: 1 lignes converties\n", "  JSON: Json/gprojet_data.json\n", "  CSV:  <PERSON><PERSON><PERSON>_projet/gprojet_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. CONVERSION - G-Projet Analysis\n", "convert_json_to_csv(\n", "    json_file_path='Json/gprojet_data.json',\n", "    csv_output_path='Su<PERSON><PERSON>_projet/gprojet_data.csv',\n", "    dataset_name='G-Projet Data'\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ G-Stock Approvisionnement: 1 lignes converties\n", "  JSON: Json/g_stockapprovisionnement.json\n", "  CSV:  Su<PERSON><PERSON>_projet/g_stockapprovisionnement.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 5. CONVERSION - G-Stock Approvisionnement\n", "convert_json_to_csv(\n", "    json_file_path='Json/g_stockapprovisionnement.json',\n", "    csv_output_path='Suivie_projet/g_stockapprovisionnement.csv',\n", "    dataset_name='G-Stock Approvisionnement'\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ G-Stock Achat: 1 lignes converties\n", "  JSON: Json/gstockachat_data.json\n", "  CSV:  <PERSON><PERSON><PERSON>_projet/gstockachat_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 6. CONVERSION - G-Stock Achat\n", "convert_json_to_csv(\n", "    json_file_path='Json/gstockachat_data.json',\n", "    csv_output_path='<PERSON><PERSON><PERSON>_projet/gstockachat_data.csv',\n", "    dataset_name='G-Stock Achat'\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ G-Stock Consommation: 1 lignes converties\n", "  JSON: Json/gstockconsommation_data.json\n", "  CSV:  <PERSON><PERSON><PERSON>_projet/gstockconsommation_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 7. CONVERSION - G-Stock Consommation\n", "convert_json_to_csv(\n", "    json_file_path='Json/gstockconsommation_data.json',\n", "    csv_output_path='Su<PERSON>ie_projet/gstockconsommation_data.csv',\n", "    dataset_name='G-Stock Consommation'\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ G-Stock Sortie: 1 lignes converties\n", "  JSON: Json/gstocksortie_data.json\n", "  CSV:  <PERSON><PERSON><PERSON>_projet/gstocksortie_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 8. CONVERSION - G-Stock Sortie\n", "convert_json_to_csv(\n", "    json_file_path='Json/gstocksortie_data.json',\n", "    csv_output_path='Su<PERSON><PERSON>_projet/gstocksortie_data.csv',\n", "    dataset_name='G-Stock Sortie'\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Inflation: 2 lignes converties\n", "  JSON: Json/donneesinflation_data.json\n", "  CSV:  Donnees externes/donneesinflation_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 9. CONVERSION - Données Inflation\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesinflation_data.json',\n", "    csv_output_path='Donnees externes/donneesinflation_data.csv',\n", "    dataset_name='Données Inflation'\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ <PERSON><PERSON><PERSON> Construction: 1 lignes converties\n", "  JSON: Json/donneesmateriauxconstruction_data.json\n", "  CSV:  Donnees externes/donneesmateriauxconstruction_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 10. CONVERSION - Données Matériaux Construction\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesmateriauxconstruction_data.json',\n", "    csv_output_path='Donnees externes/donneesmateriauxconstruction_data.csv',\n", "    dataset_name='Données <PERSON> Construction'\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Migration Interne: 1 lignes converties\n", "  JSON: Json/donneesmigrationinterne_data.json\n", "  CSV:  Donnees externes/donneesmigrationinterne_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 11. CONVERSION - Données Migration Interne\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesmigrationinterne_data.json',\n", "    csv_output_path='Donnees externes/donneesmigrationinterne_data.csv',\n", "    dataset_name='Données Migration Interne'\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Prix Mètre Carré: 2 lignes converties\n", "  JSON: Json/donneesprixmetrecarre_data.json\n", "  CSV:  Donnees externes/donneesprixmetrecarre_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 12. CONVERSION - Données Prix Mètre Carré\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesprixmetrecarre_data.json',\n", "    csv_output_path='Donnees externes/donneesprixmetrecarre_data.csv',\n", "    dataset_name='Données Prix Mètre Carré'\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Produit Intérieur Brut: 1 lignes converties\n", "  JSON: Json/donneesproduitinterieurbrut_data.json\n", "  CSV:  Donnees externes/donneesproduitinterieurbrut_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 13. CONVERSION - Données Produit Intérieur Brut\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesproduitinterieurbrut_data.json',\n", "    csv_output_path='Donnees externes/donneesproduitinterieurbrut_data.csv',\n", "    dataset_name='Données Produit Intérieur Brut'\n", ")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Projection Démographique: 1 lignes converties\n", "  JSON: Json/donneesprojectiondemographique_data.json\n", "  CSV:  Donnees externes/donneesprojectiondemographique_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 14. CONVERSION - Données Projection Démographique\n", "convert_json_to_csv(\n", "    json_file_path='Json/donneesprojectiondemographique_data.json',\n", "    csv_output_path='Donnees externes/donneesprojectiondemographique_data.csv',\n", "    dataset_name='Données Projection Démographique'\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Données Taux Prê<PERSON> I<PERSON>ob<PERSON>: 1 lignes converties\n", "  JSON: Json/donnestauxpretimmobilier_data.json\n", "  CSV:  Donnees externes/donnestauxpretimmobilier_data.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 15. CONVERSION - Données Taux Prê<PERSON>\n", "convert_json_to_csv(\n", "    json_file_path='Json/donnestauxpretimmobilier_data.json',\n", "    csv_output_path='Donnees externes/donnestauxpretimmobilier_data.csv',\n", "    dataset_name='<PERSON><PERSON><PERSON>r<PERSON> Immobili<PERSON>'\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ E-Syndic: 1 lignes converties\n", "  JSON: ./Json/E_syndic.json\n", "  CSV:  ./E_syndic.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 16. CONVERSION - E-Syndic\n", "convert_json_to_csv(\n", "    json_file_path='./Json/E_syndic.json',\n", "    csv_output_path='./E_syndic.csv',\n", "    dataset_name='E-Syndic'\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Gestion Locative: 42 lignes converties\n", "  JSON: ./Json/G_locative.json\n", "  CSV:  ./G_locative.csv\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 17. CONVERSION - Gestion Locative\n", "convert_json_to_csv(\n", "    json_file_path='./Json/G_locative.json',\n", "    csv_output_path='./G_locative.csv',\n", "    dataset_name='Gestion Locative'\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RÉSUMÉ DE LA CONVERSION\n", "print(\"=\" * 50)\n", "print(\"RÉSUMÉ DE LA CONVERSION JSON VERS CSV\")\n", "print(\"=\" * 50)\n", "print(f\"Fin de la conversion: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print()\n", "print(\"FICHIERS CSV GÉNÉRÉS DANS LES DOSSIERS:\")\n", "print(\"• Racine notebooks/: userprofile_data.csv, E_syndic.csv, G_locative.csv\")\n", "print(\"• Creation_definition_projet/: dqedata_data.csv\")\n", "print(\"• Su<PERSON><PERSON>_projet/: ecole_talents_data.csv, gprojet_data.csv, g_stockapprovisionnement.csv\")\n", "print(\"                  gstockachat_data.csv, gstockconsommation_data.csv, gstocksortie_data.csv\")\n", "print(\"• Donnees externes/: donneesinflation_data.csv, donneesmateriauxconstruction_data.csv\")\n", "print(\"                     donneesmigrationinterne_data.csv, donneesprixmetrecarre_data.csv\")\n", "print(\"                     donneesproduitinterieurbrut_data.csv, donneesprojectiondemographique_data.csv\")\n", "print(\"                     donnestauxpretimmobilier_data.csv\")\n", "print()\n", "print(\"✓ Tous les fichiers JSON ont été convertis en CSV\")\n", "print(\"✓ Les fichiers CSV sont maintenant disponibles dans leurs dossiers respectifs\")\n", "print(\"✓ Vous pouvez maintenant utiliser les notebooks individuels pour l'analyse\")\n", "print()\n", "print(\"PROCHAINES ÉTAPES:\")\n", "print(\"1. Ouvrir les notebooks individuels dans leurs dossiers\")\n", "print(\"2. Les fichiers CSV seront automatiquement chargés\")\n", "print(\"3. <PERSON><PERSON><PERSON>r les analyses et visualisations Plotly\")\n", "print()\n", "print(\"*Conversion terminée - <PERSON>dan Analytics Hub*\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}