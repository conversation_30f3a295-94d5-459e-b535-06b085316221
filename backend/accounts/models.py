from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _


class CustomUser(AbstractUser):
    username = models.Char<PERSON>ield(("username"), max_length=100, unique=True)
    last_name = models.Cha<PERSON><PERSON><PERSON>(("Last Name"), max_length=100)
    first_name = models.Char<PERSON>ield(("First Name"), max_length=100)
    email = models.EmailField(unique=True)
    kaydan_subsidiary = models.Char<PERSON>ield(
        ("kaydan subsidiary"), max_length=100, blank=True
    )
    is_verified = models.BooleanField(_("Email Verified"), default=False)

    # Email verification fields
    email_verified = models.BooleanField(default=False)
    email_verification_code = models.Char<PERSON>ield(max_length=6, null=True, blank=True)
    email_verification_code_created_at = models.DateTimeField(null=True, blank=True)

    # Fix clashing reverse accessors
    groups = models.ManyToManyField(
        "auth.Group",
        related_name="customuser_set",
        blank=True,
        help_text=_("The groups this user belongs to."),
        verbose_name=_("groups"),
    )
    user_permissions = models.ManyToManyField(
        "auth.Permission",
        related_name="customuser_set",
        blank=True,
        help_text=_("Specific permissions for this user."),
        verbose_name=_("user permissions"),
    )


class Meta:
    verbose_name = "CustomUser"
    verbose_name_plural = _("CustomUsers")


def __str__(self):
    return self.email or self.username
