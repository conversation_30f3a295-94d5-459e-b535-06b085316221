from rest_framework import serializers
from .models import CustomUser

# User serializer


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "email_verified",
        ]
        read_only_fields = ("id",)
        ref_name = "AccountUsersSerializer"


# login serializer
class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """

    username = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    password = serializers.CharField(style={"input_type": "password"})

    def validate(self, data):
        # Ensure either username or email is provided
        if not data.get("username") and not data.get("email"):
            raise serializers.ValidationError(
                "Must include either 'username' or 'email'."
            )

        from django.contrib.auth import authenticate

        username = data.get("username")
        email = data.get("email")
        password = data.get("password")

        if username:
            user = authenticate(username=username, password=password)
        elif email:
            try:
                user_obj = CustomUser.objects.get(email=email)
                user = authenticate(username=user_obj.username, password=password)
            except CustomUser.DoesNotExist:
                user = None

        if not user:
            raise serializers.ValidationError(
                "Unable to log in with provided credentials."
            )

        data["user"] = user
        return data


# Registration serializer
class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    password_confirm = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )

    class Meta:
        model = CustomUser
        fields = [
            "username",
            "email",
            "password",
            "password_confirm",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
            "kaydan_subsidiary": {"required": False},
        }

    def validate(self, data):
        # Check that the two password entries match
        if data["password"] != data["password_confirm"]:
            raise serializers.ValidationError(
                {"password_confirm": "Passwords don't match."}
            )
        return data

    def create(self, validated_data):
        # Remove password_confirm as it's not needed for creating the user
        validated_data.pop("password_confirm")

        # Create the user with the validated data
        password = validated_data.pop("password")
        user = CustomUser(**validated_data)
        user.set_password(password)
        user.save()
        return user


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for password change endpoint.
    """

    current_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    confirm_new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )

    def validate(self, data):
        # Check that the new password entries match
        if data["new_password"] != data["confirm_new_password"]:
            raise serializers.ValidationError(
                {"confirm_new_password": "New passwords don't match."}
            )
        return data


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user information.
    """

    class Meta:
        model = CustomUser
        fields = ["first_name", "last_name", "kaydan_subsidiary"]
