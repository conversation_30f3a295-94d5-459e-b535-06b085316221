from django.urls import path
from knox import views as knox_views
from .api import (
    RegisterAPI,
    LoginAPI,
    UserAPI,
    ChangePasswordAPI,
    EmailVerificationAPI,
    SendVerificationCodeAPI,
    VerifyEmailCodeAPI,
)
from . import views

urlpatterns = [
    # Knox authentication endpoints
    path("auth/login/", LoginAPI.as_view(), name="login"),
    path("auth/logout/", knox_views.LogoutView.as_view(), name="logout"),
    path("auth/logoutall/", knox_views.LogoutAllView.as_view(), name="logoutall"),
    # User registration and management
    path("auth/register/", RegisterAPI.as_view(), name="register"),
    path("auth/user/", UserAPI.as_view(), name="user"),
    path("auth/change-password/", ChangePasswordAPI.as_view(), name="change-password"),
    # Email verification
    path(
        "auth/email-verification/",
        EmailVerificationAPI.as_view(),
        name="email-verification",
    ),
    path(
        "auth/send-verification-code/",
        SendVerificationCodeAPI.as_view(),
        name="send-verification-code",
    ),
    path(
        "auth/verify-email-code/",
        VerifyEmailCodeAPI.as_view(),
        name="verify-email-code",
    ),
]
