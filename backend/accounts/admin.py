from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import CustomUser


class CustomUserAdmin(UserAdmin):
    list_display = (
        "username",
        "email",
        "first_name",
        "last_name",
        "kaydan_subsidiary",
        "email_verified",
        "is_staff",
    )
    list_filter = ("is_verified", "is_staff", "is_active", "date_joined")
    search_fields = (
        "username",
        "email",
        "first_name",
        "last_name",
        "kaydan_subsidiary",
    )

    # Add the custom fields to the fieldsets
    fieldsets = UserAdmin.fieldsets + (
        (_("Company Information"), {"fields": ("kaydan_subsidiary",)}),
        (
            _("Email Verification"),
            {
                "fields": (
                    "email_verified",
                    "email_verification_code",
                    "email_verification_code_created_at",
                )
            },
        ),
    )

    # Add custom fields to the add form
    add_fieldsets = UserAdmin.add_fieldsets + (
        (
            _("Company Information"),
            {
                "classes": ("wide",),
                "fields": ("kaydan_subsidiary", "email"),
            },
        ),
    )


admin.site.register(CustomUser, CustomUserAdmin)
