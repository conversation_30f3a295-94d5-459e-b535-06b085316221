// src/types/api.ts
// API response types matching Django backend analytics engine

import { 
    DateString, 
    ID, 
    ApiResponse, 
    PaginatedResponse, 
    Metadata,
    Money,
    Percentage 
  } from './common'

export enum ErrorType {
  NETWORK = 'NETWORK_ERROR',
  AUTH = 'AUTH_ERROR',
  VALIDATION = 'VALIDATION_ERROR',
  SERVER = 'SERVER_ERROR',
  UNKNOWN = 'UNKNOWN_ERROR'
}

export type DeliveryPerformance = 'On-time' | 'Delayed' | 'Early' | 'Complete' | 'On-track' | 'At-risk'
export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical'
export type AlertType = 'budget_overrun' | 'schedule_delay' | 'stock_shortage' | 'quality_issue' | 'performance_drop' | 'cost_spike'

/**
 * ===================================
 * DQE (Devis Quantitatif Estimatif) Data Types
 * ===================================
 */

export interface DQEData extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: Record<string, unknown>
}

export interface DQEMetrics {
  total_estimated_cost: number
  materials_cost: number
  labor_cost: number
  equipment_cost: number
  overhead_percentage: Percentage
}

/**
 * ===================================
 * G-Stock Data Types
 * ===================================
 */

export interface GStockApprovisionnement extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: GStockApprovisionnementData
}

export interface GStockApprovisionnementData {
  total_value: number
  items_count: number
  supplier_info: SupplierInfo[]
  delivery_schedule: DeliverySchedule[]
  materials: MaterialItem[]
}

export interface GStockSortie extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: GStockSortieData
}

export interface GStockSortieData {
  total_value_out: number
  items_dispatched: number
  projects_served: string[]
  dispatch_details: DispatchDetail[]
  turnover_rate: number
}

export interface GStockConsommation extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: GStockConsommationData
}

export interface GStockConsommationData {
  consumption_rate: number
  stock_levels: Record<string, number>
  critical_items: string[]
  forecasted_needs: ForecastedNeed[]
  efficiency_score: number
}

export interface GStockAchat extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: GStockAchatData
}

export interface GStockAchatData {
  total_purchases: number
  vendor_performance: VendorPerformance[]
  cost_analysis: CostAnalysis
  purchase_orders: PurchaseOrder[]
}

/**
 * Supporting G-Stock interfaces
 */
export interface SupplierInfo {
  supplier_id: ID
  supplier_name: string
  contact_info: string
  reliability_score: number
  delivery_time: number
}

export interface DeliverySchedule {
  delivery_date: DateString
  items: string[]
  status: 'pending' | 'delivered' | 'delayed'
}

export interface MaterialItem {
  item_id: ID
  item_name: string
  quantity: number
  unit_price: number
  total_value: number
  category: string
}

export interface DispatchDetail {
  project_id: ID
  project_name: string
  items_dispatched: MaterialItem[]
  dispatch_date: DateString
  requested_by: string
}

export interface ForecastedNeed {
  item_name: string
  current_stock: number
  projected_consumption: number
  reorder_point: number
  suggested_order_quantity: number
}

export interface VendorPerformance {
  vendor_id: ID
  vendor_name: string
  total_orders: number
  on_time_delivery_rate: Percentage
  quality_score: number
  cost_competitiveness: number
}

export interface CostAnalysis {
  total_spend: number
  cost_savings: number
  price_trends: PriceTrend[]
  budget_variance: number
}

export interface PriceTrend {
  item_name: string
  price_history: PricePoint[]
  trend_direction: 'up' | 'down' | 'stable'
}

export interface PricePoint {
  date: DateString
  price: number
}

export interface PurchaseOrder {
  po_id: ID
  vendor_name: string
  total_amount: number
  status: 'pending' | 'approved' | 'delivered' | 'cancelled'
  order_date: DateString
  expected_delivery: DateString
}

/**
 * ===================================
 * G-Projet Data Types
 * ===================================
 */

export interface GProjet extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: GProjetData
}

export interface GProjetData {
  projects: ProjectInfo[]
  overall_progress: number
  milestones: Milestone[]
  resource_allocation: ResourceAllocation[]
  timeline: ProjectTimeline[]
}

export interface ProjectInfo {
  project_id: ID
  project_name: string
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled'
  progress_percentage: Percentage
  start_date: DateString
  expected_end_date: DateString
  actual_end_date?: DateString
  budget: number
  spent: number
}

export interface Milestone {
  milestone_id: ID
  project_id: ID
  milestone_name: string
  target_date: DateString
  completion_date?: DateString
  status: 'pending' | 'completed' | 'overdue'
  dependencies: ID[]
}

export interface ResourceAllocation {
  project_id: ID
  resource_type: 'human' | 'equipment' | 'material'
  resource_name: string
  allocated_quantity: number
  utilization_rate: Percentage
}

export interface ProjectTimeline {
  project_id: ID
  phase_name: string
  start_date: DateString
  end_date: DateString
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed'
  critical_path: boolean
}

/**
 * ===================================
 * Ecole des Talents Data Types
 * ===================================
 */

export interface EcoleTalents extends Metadata {
  id: ID
  title: string | null
  description: string | null
  data: EcoleTalentsData
}

export interface EcoleTalentsData {
  total_students: number
  active_programs: number
  completion_rates: CompletionRate[]
  performance_metrics: PerformanceMetric[]
  instructor_data: InstructorData[]
}

export interface CompletionRate {
  program_name: string
  enrolled_students: number
  completed_students: number
  completion_rate: Percentage
  average_duration: number
}

export interface PerformanceMetric {
  metric_name: string
  metric_value: number
  benchmark: number
  trend: 'improving' | 'declining' | 'stable'
}

export interface InstructorData {
  instructor_id: ID
  instructor_name: string
  subjects: string[]
  student_feedback_score: number
  courses_taught: number
}

/**
 * ===================================
 * Analytics Engine Response Types
 * ===================================
 */

export interface ProjectAnalytics extends Metadata {
  id: ID
  project_name: string
  project_id?: string
  
  // Financial Metrics
  estimated_budget?: number
  actual_cost?: number
  budget_variance?: number
  budget_variance_percentage?: Percentage
  
  // Timeline Metrics
  planned_duration?: number
  actual_duration?: number
  completion_percentage?: Percentage
  
  // Stock Efficiency Metrics
  stock_utilization_rate?: Percentage
  waste_percentage?: Percentage
  stock_cost_efficiency?: Percentage
  
  // Performance Indicators
  profitability_index?: Percentage
  delivery_performance?: 'On-time' | 'Delayed' | 'Early' | 'Complete' | 'On-track' | 'At-risk'
  
  // Meta Information
  analysis_date: DateString
  data_sources: string[]
}

export interface StockAnalytics extends Metadata {
  id: ID
  analysis_period: 'monthly' | 'quarterly' | 'yearly'
  period_start: DateString
  period_end: DateString
  
  // Stock Metrics
  total_stock_value?: number
  stock_turnover_ratio?: number
  average_stock_level?: number
  stockout_incidents: number
  
  // Efficiency Metrics
  carrying_cost_percentage?: Percentage
  demand_forecast_accuracy?: Percentage
  supplier_performance?: Percentage
  
  // Material Categories Performance
  materials_performance: Record<string, unknown>
}

export interface BusinessKPIs extends Metadata {
  id: ID
  kpi_date: DateString
  kpi_type: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  
  // Financial KPIs
  total_revenue?: number
  gross_profit_margin?: Percentage
  operating_margin?: Percentage
  roi_percentage?: Percentage
  
  // Operational KPIs
  project_completion_rate?: Percentage
  average_project_delay?: number
  customer_satisfaction_score?: Percentage
  
  // Market KPIs
  market_share_percentage?: Percentage
  sales_conversion_rate?: Percentage
  
  // Risk Indicators
  budget_overrun_frequency?: Percentage
  quality_incident_rate?: Percentage
  
  // Raw data for detailed analysis
  kpi_data: Record<string, unknown>
}

export interface AlertSystem extends Metadata {
  id: ID
  alert_type: 'budget_overrun' | 'schedule_delay' | 'stock_shortage' | 'quality_issue' | 'performance_drop' | 'cost_spike'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  
  // Related objects
  project_name?: string
  affected_area?: string
  
  // Alert data
  threshold_value?: number
  actual_value?: number
  variance_percentage?: Percentage
  
  // Status
  is_resolved: boolean
  resolved_at?: DateString
}

/**
 * ===================================
 * Dashboard Summary Types
 * ===================================
 */

export interface DashboardSummary {
  // Overview metrics
  total_projects: number
  active_projects: number
  completed_projects: number
  projects_at_risk: number
  
  // Financial summary
  total_budget: number
  total_spent: number
  average_budget_variance: Percentage
  total_savings: number
  
  // Performance metrics
  overall_completion_rate: Percentage
  on_time_delivery_rate: Percentage
  average_project_duration: number
  
  // Stock metrics
  total_stock_value: number
  stock_efficiency_score: Percentage
  active_alerts_count: number
  
  // Trends
  performance_trend: 'improving' | 'declining' | 'stable' | 'insufficient_data'
  budget_trend: 'improving' | 'declining' | 'stable' | 'insufficient_data'
  
  // Last updated
  last_analysis_date: DateString
}

export interface AnalyticsInsight {
  insight_type: 'budget_performance' | 'completion_performance' | 'stock_performance' | 'risk_assessment'
  title: string
  description: string
  impact_level: 'high' | 'medium' | 'low'
  recommendation: string
  supporting_data: Record<string, unknown>
  confidence_score: Percentage
  created_at: DateString
}

export interface PerformanceTrend {
  date: DateString
  metric_name: string
  metric_value: number
  trend_direction: 'up' | 'down' | 'stable'
  change_percentage: Percentage
}

/**
 * ===================================
 * API Request/Response Wrappers
 * ===================================
 */

// DQE API Responses
export type DQEDataApiResponse = ApiResponse<DQEData>
export type DQEDataListApiResponse = ApiResponse<DQEData[]>

// G-Stock API Responses
export type GStockApprovisionnementApiResponse = ApiResponse<GStockApprovisionnement>
export type GStockSortieApiResponse = ApiResponse<GStockSortie>
export type GStockConsommationApiResponse = ApiResponse<GStockConsommation>
export type GStockAchatApiResponse = ApiResponse<GStockAchat>

// G-Projet API Responses
export type GProjetApiResponse = ApiResponse<GProjet>
export type GProjetListApiResponse = ApiResponse<GProjet[]>

// Ecole des Talents API Responses
export type EcoleTalentsApiResponse = ApiResponse<EcoleTalents>
export type EcoleTalentsListApiResponse = ApiResponse<EcoleTalents[]>

// Analytics API Responses
export type ProjectAnalyticsApiResponse = ApiResponse<ProjectAnalytics>
export type ProjectAnalyticsListApiResponse = PaginatedResponse<ProjectAnalytics>
export type StockAnalyticsApiResponse = ApiResponse<StockAnalytics>
export type BusinessKPIsApiResponse = ApiResponse<BusinessKPIs>
export type BusinessKPIsListApiResponse = ApiResponse<BusinessKPIs[]>
export type AlertSystemApiResponse = ApiResponse<AlertSystem>
export type AlertSystemListApiResponse = PaginatedResponse<AlertSystem>

// Dashboard API Responses
export type DashboardSummaryApiResponse = ApiResponse<DashboardSummary>
export type AnalyticsInsightApiResponse = ApiResponse<AnalyticsInsight[]>
export type PerformanceTrendApiResponse = ApiResponse<PerformanceTrend[]>

// Analytics Operations
export type RunAnalysisApiResponse = ApiResponse<{
  projects_analyzed: number
  alerts_generated: number
  analysis_timestamp: DateString
}>

/**
 * ===================================
 * Filter and Query Types
 * ===================================
 */

export interface ProjectAnalyticsFilters {
  project_name?: string
  performance?: 'On-track' | 'At-risk' | 'Complete'
  start_date?: DateString
  end_date?: DateString
  budget_min?: number
  budget_max?: number
}

export interface AlertFilters {
  alert_type?: AlertSystem['alert_type']
  severity?: AlertSystem['severity']
  resolved?: boolean
  project_name?: string
  date_from?: DateString
  date_to?: DateString
}

export interface KPIFilters {
  kpi_type?: BusinessKPIs['kpi_type']
  date_from?: DateString
  date_to?: DateString
}

/**
 * ===================================
 * Export and Reporting Types
 * ===================================
 */

export interface ExportRequest {
  data_type: 'projects' | 'stock' | 'kpis' | 'alerts'
  format: 'csv' | 'xlsx' | 'pdf' | 'json'
  filters?: Record<string, unknown>
  date_range?: {
    start: DateString
    end: DateString
  }
}

export interface ReportGenerationRequest {
  report_type: 'executive_summary' | 'operational_report' | 'analytical_deep_dive'
  time_period: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  include_charts: boolean
  include_recommendations: boolean
  delivery_method: 'download' | 'email'
}

/**
 * ===================================
 * Alert System Types
 * ===================================
 */

export interface CreateAlertRequest {
  alert_type: AlertType
  severity: AlertSeverity
  title: string
  message: string
  project_name?: string
  affected_area?: string
  threshold_value?: number
  actual_value?: number
  variance_percentage?: Percentage
}

export interface UpdateAlertRequest {
  severity?: AlertSeverity
  title?: string
  message?: string
  is_resolved?: boolean
  resolved_at?: DateString
  threshold_value?: number
  actual_value?: number
  variance_percentage?: Percentage
}

/**
 * ===================================
 * User Management Types
 * ===================================
 */

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: {
    email: boolean
    push: boolean
    alerts: boolean
  }
  dashboard: {
    defaultView: string
    refreshInterval: number
  }
}

export interface UserActivity {
  id: string
  user_id: string
  action: string
  timestamp: DateString
  details: Record<string, unknown>
  ip_address?: string
  user_agent?: string
}

export interface UserSession {
  id: string
  user_id: string
  token: string
  created_at: DateString
  expires_at: DateString
  last_activity: DateString
  device_info: {
    browser: string
    os: string
    ip_address: string
  }
}

export interface User {
  id: string
  email: string
  username: string
  is_active: boolean
  created_at: DateString
  last_login?: DateString
}

export interface UserProfile {
  user_id: string
  first_name: string
  last_name: string
  phone?: string
  department?: string
  position?: string
  avatar_url?: string
}

export interface UserRole {
  id: string
  name: 'admin' | 'executive' | 'manager' | 'analyst' | 'viewer'
  description: string
}

export interface UserPermissions {
  can_view_dashboard: boolean
  can_manage_users: boolean
  can_generate_reports: boolean
  can_manage_projects: boolean
  can_manage_stock: boolean
}

/**
 * ===================================
 * Report Types
 * ===================================
 */

export interface ReportConfig {
  report_type: 'executive_summary' | 'operational_report' | 'analytical_deep_dive'
  time_period: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  include_charts: boolean
  include_recommendations: boolean
  delivery_method: 'download' | 'email'
  filters?: Record<string, unknown>
}

export interface ReportResponse {
  report_id: string
  status: 'generating' | 'completed' | 'failed'
  download_url?: string
  generated_at: DateString
  expires_at: DateString
}

export interface ReportHistory {
  report_id: string
  config: ReportConfig
  status: 'success' | 'failed'
  generated_at: DateString
  download_url?: string
}

export interface ScheduledReportConfig extends ReportConfig {
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly'
    time: string
    recipients: string[]
  }
}

export interface ScheduledReport {
  id: string
  config: ScheduledReportConfig
  last_run?: DateString
  next_run: DateString
  status: 'active' | 'paused' | 'failed'
}

export interface ExportConfig {
  data_type: 'projects' | 'stock' | 'kpis' | 'alerts'
  format: 'csv' | 'xlsx' | 'pdf' | 'json'
  filters?: Record<string, unknown>
  date_range?: {
    start: DateString
    end: DateString
  }
}

export interface TokenVerificationResponse {
  valid: boolean
  expires_at?: DateString
}

export interface AlertStatistics {
  total_alerts: number
  active_alerts: number
  resolved_alerts: number
  alerts_by_severity: Record<AlertSeverity, number>
  alerts_by_type: Record<AlertType, number>
  average_resolution_time: number
}

export interface ApiError {
  status: 'error'
  message: string
  details?: Record<string, unknown>
  code: number | string
  type: ErrorType
}