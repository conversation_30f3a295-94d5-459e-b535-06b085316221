// src/types/navigation.ts
// Routing, navigation, breadcrumbs, and role-based access control types

import { ReactNode } from 'react'
import { UserRole, UserPermissions } from './auth'
import { DashboardType } from './analytics'
import { ID, Color } from './common'

/**
 * Route Definition Types
 */

export interface Route {
  id: string
  path: string
  name: string
  title?: string
  description?: string
  component: React.ComponentType
  layout?: LayoutType
  protection: RouteProtection
  metadata?: RouteMetadata
  children?: Route[]
}

export interface RouteProtection {
  requireAuth: boolean
  requiredRoles?: UserRole[]
  requiredPermissions?: (keyof UserPermissions)[]
  redirectTo?: string
  fallbackComponent?: React.ComponentType
}

export interface RouteMetadata {
  category?: string
  tags?: string[]
  priority?: number
  hidden?: boolean
  external?: boolean
  beta?: boolean
  deprecated?: boolean
}

export type LayoutType = 
  | 'auth'           // Authentication layout (login, OTP)
  | 'dashboard'      // Main dashboard layout with sidebar
  | 'full'           // Full-screen layout (reports, presentations)
  | 'minimal'        // Minimal layout (error pages, maintenance)
  | 'embedded'       // Embedded layout (widgets, iframes)

/**
 * Navigation Menu Types
 */

export interface NavMenuItem {
  id: string
  label: string
  icon?: string
  path?: string
  type: MenuItemType
  badge?: MenuBadge
  children?: NavMenuItem[]
  roles?: UserRole[]
  permissions?: (keyof UserPermissions)[]
  metadata?: MenuItemMetadata
  onClick?: () => void
}

export type MenuItemType = 
  | 'link'           // Regular navigation link
  | 'group'          // Menu group/section
  | 'divider'        // Visual separator
  | 'action'         // Action button (logout, settings)
  | 'external'       // External link
  | 'dropdown'       // Dropdown submenu

export interface MenuBadge {
  text: string
  color: Color
  variant: 'solid' | 'outline' | 'subtle'
  pulse?: boolean
}

export interface MenuItemMetadata {
  order?: number
  shortcut?: string
  tooltip?: string
  new?: boolean
  featured?: boolean
  disabled?: boolean
}

/**
 * Sidebar Navigation Types
 */

export interface SidebarConfig {
  variant: SidebarVariant
  width: SidebarWidth
  position: SidebarPosition
  behavior: SidebarBehavior
  styling: SidebarStyling
}

export type SidebarVariant = 
  | 'default'        // Standard sidebar with full menu
  | 'compact'        // Compact sidebar with icons only
  | 'overlay'        // Overlay sidebar (mobile)
  | 'mini'           // Mini sidebar that expands on hover

export type SidebarWidth = 'narrow' | 'normal' | 'wide' | 'auto'

export type SidebarPosition = 'left' | 'right'

export interface SidebarBehavior {
  collapsible: boolean
  auto_collapse_on_mobile: boolean
  remember_state: boolean
  hover_expand: boolean
  click_outside_to_close: boolean
}

export interface SidebarStyling {
  theme: 'light' | 'dark' | 'auto'
  background: string
  border: boolean
  shadow: boolean
  rounded: boolean
}

export interface SidebarState {
  isOpen: boolean
  isCollapsed: boolean
  isPinned: boolean
  width: number
  variant: SidebarVariant
}

/**
 * Breadcrumb Types
 */

export interface BreadcrumbItem {
  id: string
  label: string
  path?: string
  icon?: string
  disabled?: boolean
  current?: boolean
  metadata?: Record<string, unknown>
}

export interface BreadcrumbConfig {
  show_home: boolean
  show_icons: boolean
  max_items: number
  separator: string
  truncate_middle: boolean
  interactive: boolean
}

export interface BreadcrumbState {
  items: BreadcrumbItem[]
  current_path: string
  is_loading: boolean
}

/**
 * Header Navigation Types
 */

export interface HeaderConfig {
  variant: HeaderVariant
  height: HeaderHeight
  position: HeaderPosition
  content: HeaderContent
  styling: HeaderStyling
}

export type HeaderVariant = 
  | 'default'        // Standard header
  | 'compact'        // Compact header
  | 'transparent'    // Transparent header
  | 'glass'          // Glass morphism header

export type HeaderHeight = 'small' | 'medium' | 'large'

export type HeaderPosition = 'fixed' | 'static' | 'sticky'

export interface HeaderContent {
  show_logo: boolean
  show_breadcrumbs: boolean
  show_search: boolean
  show_notifications: boolean
  show_user_menu: boolean
  custom_actions?: HeaderAction[]
}

export interface HeaderAction {
  id: string
  type: 'button' | 'dropdown' | 'search' | 'custom'
  icon?: string
  label?: string
  onClick?: () => void
  component?: React.ComponentType
  roles?: UserRole[]
}

export interface HeaderStyling {
  background: string
  border: boolean
  shadow: boolean
  blur: boolean
}

/**
 * Tab Navigation Types
 */

export interface TabConfig {
  id: string
  label: string
  path: string
  icon?: string
  badge?: MenuBadge
  disabled?: boolean
  closable?: boolean
  order?: number
}

export interface TabState {
  active_tab: string
  tabs: TabConfig[]
  history: string[]
  max_tabs?: number
}

/**
 * Mobile Navigation Types
 */

export interface MobileNavConfig {
  type: MobileNavType
  items: MobileNavItem[]
  behavior: MobileNavBehavior
}

export type MobileNavType = 
  | 'bottom_tabs'    // Bottom tab navigation
  | 'drawer'         // Side drawer navigation
  | 'modal'          // Modal navigation menu

export interface MobileNavItem {
  id: string
  label: string
  icon: string
  path: string
  badge?: MenuBadge
  roles?: UserRole[]
}

export interface MobileNavBehavior {
  auto_hide_on_scroll: boolean
  show_labels: boolean
  haptic_feedback: boolean
  animation_type: 'slide' | 'fade' | 'bounce'
}

/**
 * Search Navigation Types
 */

export interface SearchConfig {
  enabled: boolean
  placeholder: string
  sources: SearchSource[]
  shortcuts: SearchShortcut[]
  recent_searches: boolean
  suggestions: boolean
}

export interface SearchSource {
  id: string
  name: string
  type: SearchSourceType
  endpoint?: string
  icon?: string
  weight?: number
}

export type SearchSourceType = 
  | 'pages'          // Navigation pages
  | 'projects'       // Project data
  | 'analytics'      // Analytics data
  | 'alerts'         // Alert data
  | 'users'          // User data
  | 'help'           // Help documentation

export interface SearchShortcut {
  key: string
  label: string
  action: () => void
}

export interface SearchResult {
  id: string
  title: string
  description?: string
  path?: string
  type: SearchSourceType
  icon?: string
  score: number
  metadata?: Record<string, unknown>
}

/**
 * Context Menu Types
 */

export interface ContextMenuConfig {
  id: string
  items: ContextMenuItem[]
  position: MenuPosition
  trigger: ContextMenuTrigger
}

export interface ContextMenuItem {
  id: string
  label: string
  icon?: string
  action?: () => void
  disabled?: boolean
  divider?: boolean
  submenu?: ContextMenuItem[]
  shortcut?: string
}

export interface MenuPosition {
  x: number
  y: number
  anchor?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
}

export type ContextMenuTrigger = 'right-click' | 'long-press' | 'button-click'

/**
 * Navigation State Management Types
 */

export interface NavigationState {
  current_route: string
  previous_route?: string
  route_history: string[]
  sidebar: SidebarState
  breadcrumbs: BreadcrumbState
  tabs: TabState
  mobile_nav_open: boolean
  context_menu?: ContextMenuConfig
  search_open: boolean
  search_query: string
  search_results: SearchResult[]
}

export interface NavigationContext {
  // Current navigation state
  state: NavigationState
  
  // Route navigation
  navigate: (path: string, options?: NavigationOptions) => void
  goBack: () => void
  goForward: () => void
  replace: (path: string) => void
  
  // Sidebar controls
  toggleSidebar: () => void
  collapseSidebar: () => void
  expandSidebar: () => void
  setSidebarVariant: (variant: SidebarVariant) => void
  
  // Tab management
  openTab: (tab: TabConfig) => void
  closeTab: (tabId: string) => void
  switchTab: (tabId: string) => void
  
  // Search functionality
  openSearch: () => void
  closeSearch: () => void
  search: (query: string) => Promise<SearchResult[]>
  
  // Mobile navigation
  toggleMobileNav: () => void
  
  // Context menu
  showContextMenu: (config: ContextMenuConfig) => void
  hideContextMenu: () => void
  
  // Breadcrumbs
  updateBreadcrumbs: (items: BreadcrumbItem[]) => void
  
  // Navigation guards
  canNavigate: (path: string) => boolean
  checkPermissions: (route: Route) => boolean
}

export interface NavigationOptions {
  replace?: boolean
  state?: Record<string, unknown>
  scroll?: boolean
  preserveQuery?: boolean
}

/**
 * Route Guards and Protection Types
 */

export interface RouteGuard {
  id: string
  name: string
  check: (route: Route, user?: any) => Promise<GuardResult>
  priority?: number
}

export interface GuardResult {
  allowed: boolean
  redirect?: string
  message?: string
  component?: React.ComponentType
}

export interface RouteGuardContext {
  guards: RouteGuard[]
  addGuard: (guard: RouteGuard) => void
  removeGuard: (guardId: string) => void
  checkRoute: (route: Route) => Promise<GuardResult>
}

/**
 * Navigation Configuration Types
 */

export interface NavigationConfig {
  routes: Route[]
  menu_items: NavMenuItem[]
  sidebar: SidebarConfig
  header: HeaderConfig
  breadcrumbs: BreadcrumbConfig
  search: SearchConfig
  mobile_nav: MobileNavConfig
  route_guards: RouteGuard[]
}

/**
 * Dashboard Navigation Types
 */

export interface DashboardNavigation {
  type: DashboardType
  sections: DashboardSection[]
  quick_actions: QuickAction[]
  user_shortcuts: UserShortcut[]
}

export interface DashboardSection {
  id: string
  title: string
  description?: string
  icon?: string
  items: DashboardNavItem[]
  roles?: UserRole[]
  order?: number
}

export interface DashboardNavItem {
  id: string
  title: string
  description?: string
  path: string
  icon?: string
  badge?: MenuBadge
  roles?: UserRole[]
  permissions?: (keyof UserPermissions)[]
  featured?: boolean
}

export interface QuickAction {
  id: string
  label: string
  icon: string
  action: () => void
  shortcut?: string
  roles?: UserRole[]
}

export interface UserShortcut {
  id: string
  label: string
  path: string
  icon?: string
  order?: number
  user_id: ID
}

/**
 * Navigation Analytics Types
 */

export interface NavigationAnalytics {
  page_views: PageView[]
  user_journeys: UserJourney[]
  popular_paths: PopularPath[]
  exit_points: ExitPoint[]
  navigation_performance: NavigationPerformance
}

export interface PageView {
  path: string
  title: string
  user_id?: ID
  timestamp: string
  duration?: number
  referrer?: string
}

export interface UserJourney {
  user_id: ID
  session_id: string
  steps: JourneyStep[]
  start_time: string
  end_time: string
  completed: boolean
}

export interface JourneyStep {
  path: string
  timestamp: string
  action?: string
  duration: number
}

export interface PopularPath {
  path: string
  visits: number
  unique_visitors: number
  bounce_rate: number
  avg_duration: number
}

export interface ExitPoint {
  path: string
  exit_rate: number
  previous_path?: string
  timestamp: string
}

export interface NavigationPerformance {
  avg_load_time: number
  avg_navigation_time: number
  error_rate: number
  bounce_rate: number
}

/**
 * Hook Return Types
 */

export interface UseNavigationReturn extends NavigationContext {}

export interface UseRouteGuardsReturn extends RouteGuardContext {}

export interface UseBreadcrumbsReturn {
  items: BreadcrumbItem[]
  addItem: (item: BreadcrumbItem) => void
  removeItem: (itemId: string) => void
  updateItem: (itemId: string, updates: Partial<BreadcrumbItem>) => void
  reset: () => void
}

export interface UseSidebarReturn {
  state: SidebarState
  toggle: () => void
  collapse: () => void
  expand: () => void
  setVariant: (variant: SidebarVariant) => void
  setWidth: (width: number) => void
}

export interface UseSearchReturn {
  isOpen: boolean
  query: string
  results: SearchResult[]
  isLoading: boolean
  open: () => void
  close: () => void
  search: (query: string) => void
  selectResult: (result: SearchResult) => void
  clearResults: () => void
}

/**
 * Utility Types
 */

export type NavigationEventType = 
  | 'route_change'
  | 'sidebar_toggle'
  | 'tab_switch'
  | 'search_open'
  | 'context_menu_open'

export interface NavigationEvent {
  type: NavigationEventType
  timestamp: string
  data: Record<string, unknown>
  user_id?: ID
}

export interface RouteChangeEvent extends NavigationEvent {
  type: 'route_change'
  data: {
    from: string
    to: string
    method: 'push' | 'replace' | 'back' | 'forward'
  }
}

export type NavigationErrorType = 
  | 'route_not_found'
  | 'access_denied'
  | 'guard_failure'
  | 'navigation_blocked'

export interface NavigationError {
  type: NavigationErrorType
  message: string
  path: string
  timestamp: string
  user_id?: ID
}