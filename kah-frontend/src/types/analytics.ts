// src/types/analytics.ts
// Dashboard data types, chart structures, KPI metrics, and visualization data formats

import { 
    DateString, 
    ID, 
    Percentage, 
    TimeRangeOption,
    DateRange,
    Color,
    SortConfig,
    ExportFormat
  } from './common'
  
  import {
    ProjectAnalytics,
    StockAnalytics,
    BusinessKPIs,
    AlertSystem,
    DeliveryPerformance,
    AlertSeverity,
    AlertType
  } from './api'
  
  /**
   * Dashboard Types
   */
  
  export type DashboardType = 'executive' | 'operational' | 'analytical'
  
  export interface DashboardConfig {
    type: DashboardType
    layout: DashboardLayout
    widgets: DashboardWidget[]
    filters: FilterState
    preferences: DashboardPreferences
  }
  
  export interface DashboardLayout {
    columns: number
    rows: number
    gap: number
    responsive: ResponsiveLayout
  }
  
  export interface ResponsiveLayout {
    mobile: { columns: number; rows: number }
    tablet: { columns: number; rows: number }
    desktop: { columns: number; rows: number }
  }
  
  export interface DashboardWidget {
    id: string
    type: WidgetType
    title: string
    description?: string
    position: WidgetPosition
    size: WidgetSize
    data_source: string
    config: WidgetConfig
    is_visible: boolean
    can_resize: boolean
    can_move: boolean
  }
  
  export type WidgetType = 
    | 'kpi_metric'
    | 'line_chart'
    | 'bar_chart'
    | 'pie_chart'
    | 'area_chart'
    | 'scatter_plot'
    | 'heatmap'
    | 'gauge_chart'
    | 'progress_bar'
    | 'data_table'
    | 'alert_feed'
    | 'trend_indicator'
    | 'comparison_chart'
    | 'geographical_map'
  
  export interface WidgetPosition {
    x: number
    y: number
    z_index?: number
  }
  
  export interface WidgetSize {
    width: number
    height: number
    min_width?: number
    min_height?: number
    max_width?: number
    max_height?: number
  }
  
  export interface WidgetConfig {
    refresh_interval?: number // seconds
    auto_refresh: boolean
    show_legend: boolean
    show_grid: boolean
    color_scheme: ColorScheme
    animation_enabled: boolean
    interactive: boolean
    export_enabled: boolean
  }
  
  /**
   * Filter and Query Types
   */
  
  export interface FilterState {
    time_range: TimeRangeFilter
    projects: ProjectFilter[]
    status: StatusFilter[]
    categories: CategoryFilter[]
    custom_filters: CustomFilter[]
    search_query?: string
  }
  
  export interface TimeRangeFilter {
    type: TimeRangeOption
    custom_range?: DateRange
    comparison_period?: DateRange
    granularity: TimeGranularity
  }
  
  export type TimeGranularity = 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year'
  
  export interface ProjectFilter {
    project_id: ID
    project_name: string
    include_subprojects: boolean
  }
  
  export interface StatusFilter {
    type: 'project_status' | 'alert_status' | 'delivery_status'
    values: string[]
  }
  
  export interface CategoryFilter {
    category: string
    subcategories: string[]
  }
  
  export interface CustomFilter {
    field: string
    operator: FilterOperator
    value: FilterValue
    label: string
  }
  
  export type FilterOperator = 
    | 'equals'
    | 'not_equals'
    | 'greater_than'
    | 'less_than'
    | 'greater_equal'
    | 'less_equal'
    | 'contains'
    | 'not_contains'
    | 'in'
    | 'not_in'
    | 'between'
    | 'is_null'
    | 'is_not_null'
  
  export type FilterValue = string | number | boolean | string[] | number[] | DateRange
  
  /**
   * KPI and Metrics Types
   */
  
  export interface KPIMetric {
    id: string
    name: string
    value: number
    unit: string
    format: MetricFormat
    trend: TrendData
    target?: number
    threshold: MetricThreshold
    color: Color
    icon?: string
    description?: string
    last_updated: DateString
  }
  
  export type MetricFormat = 
    | 'number'
    | 'percentage'
    | 'currency'
    | 'duration'
    | 'ratio'
    | 'count'
  
  export interface TrendData {
    direction: TrendDirection
    change_value: number
    change_percentage: Percentage
    period: string
    confidence: TrendConfidence
  }
  
  export type TrendDirection = 'up' | 'down' | 'stable' | 'unknown'
  
  export type TrendConfidence = 'high' | 'medium' | 'low'
  
  export interface MetricThreshold {
    critical: { min?: number; max?: number }
    warning: { min?: number; max?: number }
    good: { min?: number; max?: number }
  }
  
  export interface KPIGroup {
    id: string
    name: string
    metrics: KPIMetric[]
    weight: number
    overall_score: number
  }
  
  /**
   * Chart Data Types
   */
  
  export interface ChartDataPoint {
    x: string | number | Date
    y: number
    label?: string
    color?: string
    metadata?: Record<string, unknown>
  }
  
  export interface TimeSeriesDataPoint extends ChartDataPoint {
    timestamp: DateString
    value: number
    trend?: TrendDirection
  }
  
  export interface MultiSeriesDataPoint {
    timestamp: DateString
    series: Record<string, number>
    metadata?: Record<string, unknown>
  }
  
  export interface ComparisonDataPoint {
    category: string
    current_value: number
    previous_value: number
    target_value?: number
    variance: number
    variance_percentage: Percentage
  }
  
  export interface HeatmapDataPoint {
    x: string | number
    y: string | number
    value: number
    color_intensity: number
    tooltip?: string
  }
  
  export interface GeographicalDataPoint {
    location: string
    latitude: number
    longitude: number
    value: number
    label: string
    metadata?: Record<string, unknown>
  }
  
  /**
   * Chart Configuration Types
   */
  
  export interface ChartConfig {
    type: ChartType
    data: ChartData
    options: ChartOptions
    style: ChartStyle
  }
  
  export type ChartType = 
    | 'line'
    | 'bar'
    | 'column'
    | 'pie'
    | 'doughnut'
    | 'area'
    | 'scatter'
    | 'bubble'
    | 'heatmap'
    | 'gauge'
    | 'funnel'
    | 'waterfall'
    | 'candlestick'
  
  export interface ChartData {
    datasets: ChartDataset[]
    labels?: string[]
    categories?: string[]
  }
  
  export interface ChartDataset {
    id: string
    name: string
    data: ChartDataPoint[]
    color: string
    fill?: boolean
    line_style?: LineStyle
    marker_style?: MarkerStyle
  }
  
  export interface ChartOptions {
    responsive: boolean
    maintain_aspect_ratio: boolean
    animation: AnimationConfig
    interaction: InteractionConfig
    legend: LegendConfig
    tooltip: TooltipConfig
    axes: AxesConfig
    grid: GridConfig
  }
  
  export interface ChartStyle {
    theme: 'light' | 'dark' | 'auto'
    color_scheme: ColorScheme
    font_family: string
    font_size: number
    border_radius: number
    shadows: boolean
  }
  
  export type LineStyle = 'solid' | 'dashed' | 'dotted'
  
  export interface MarkerStyle {
    shape: 'circle' | 'square' | 'triangle' | 'diamond'
    size: number
    border_width: number
  }
  
  export interface AnimationConfig {
    enabled: boolean
    duration: number
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out'
    delay: number
  }
  
  export interface InteractionConfig {
    hover_enabled: boolean
    click_enabled: boolean
    zoom_enabled: boolean
    pan_enabled: boolean
    brush_enabled: boolean
  }
  
  export interface LegendConfig {
    visible: boolean
    position: 'top' | 'bottom' | 'left' | 'right'
    alignment: 'start' | 'center' | 'end'
    clickable: boolean
  }
  
  export interface TooltipConfig {
    enabled: boolean
    format: string
    background_color: string
    text_color: string
    border_color: string
    show_color_box: boolean
  }
  
  export interface AxesConfig {
    x_axis: AxisConfig
    y_axis: AxisConfig
    y2_axis?: AxisConfig
  }
  
  export interface AxisConfig {
    visible: boolean
    title: string
    min?: number
    max?: number
    tick_interval?: number
    format: string
    grid_lines: boolean
    zero_line: boolean
  }
  
  export interface GridConfig {
    visible: boolean
    color: string
    line_width: number
    dash_pattern?: number[]
  }
  
  /**
   * Color and Theme Types
   */
  
  export type ColorScheme = 
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'monochrome'
    | 'rainbow'
    | 'cool'
    | 'warm'
  
  export interface ColorPalette {
    name: string
    colors: string[]
    background: string
    text: string
    border: string
  }
  
  /**
   * Performance and Analysis Types
   */
  
  export interface PerformanceMetrics {
    projects: ProjectPerformanceMetrics
    financial: FinancialPerformanceMetrics
    operational: OperationalPerformanceMetrics
    stock: StockPerformanceMetrics
  }
  
  export interface ProjectPerformanceMetrics {
    total_projects: number
    completed_projects: number
    on_time_delivery_rate: Percentage
    budget_adherence_rate: Percentage
    average_completion_time: number
    quality_score: number
  }
  
  export interface FinancialPerformanceMetrics {
    total_revenue: number
    total_cost: number
    profit_margin: Percentage
    roi: Percentage
    cost_variance: Percentage
    revenue_growth: Percentage
  }
  
  export interface OperationalPerformanceMetrics {
    efficiency_score: number
    productivity_index: number
    resource_utilization: Percentage
    waste_reduction: Percentage
    customer_satisfaction: number
  }
  
  export interface StockPerformanceMetrics {
    inventory_turnover: number
    stock_accuracy: Percentage
    carrying_cost: number
    stockout_frequency: number
    supplier_performance: number
  }
  
  /**
   * Alert and Notification Types
   */
  
  export interface AlertSummary {
    total_alerts: number
    critical_alerts: number
    unresolved_alerts: number
    alerts_by_type: Record<AlertType, number>
    alerts_by_severity: Record<AlertSeverity, number>
    recent_alerts: AlertSystem[]
  }
  
  export interface NotificationPreference {
    alert_type: AlertType
    severity_threshold: AlertSeverity
    delivery_method: NotificationMethod[]
    frequency: NotificationFrequency
    enabled: boolean
  }
  
  export type NotificationMethod = 'email' | 'push' | 'sms' | 'in_app'
  
  export type NotificationFrequency = 'immediate' | 'hourly' | 'daily' | 'weekly'
  
  /**
   * Dashboard Preferences Types
   */
  
  export interface DashboardPreferences {
    default_time_range: TimeRangeOption
    auto_refresh_interval: number
    show_animations: boolean
    compact_view: boolean
    show_tooltips: boolean
    color_scheme: ColorScheme
    language: 'en' | 'fr'
    currency: string
    timezone: string
    favorite_widgets: string[]
    hidden_widgets: string[]
    custom_layouts: Record<string, DashboardLayout>
  }
  
  /**
   * Export and Sharing Types
   */
  
  export interface ExportOptions {
    format: ExportFormat
    include_charts: boolean
    include_data: boolean
    date_range: DateRange
    filters: FilterState
    quality: 'low' | 'medium' | 'high'
  }
  
  export interface ShareConfig {
    type: 'link' | 'embed' | 'export'
    permissions: SharePermissions
    expiry_date?: DateString
    password_protected: boolean
    allow_comments: boolean
  }
  
  export interface SharePermissions {
    can_view: boolean
    can_interact: boolean
    can_download: boolean
    can_comment: boolean
  }
  
  /**
   * Real-time Data Types
   */
  
  export interface RealTimeUpdate {
    timestamp: DateString
    type: UpdateType
    target: string
    data: Record<string, unknown>
    priority: UpdatePriority
  }
  
  export type UpdateType = 
    | 'metric_update'
    | 'alert_created'
    | 'alert_resolved'
    | 'project_status_change'
    | 'data_refresh'
    | 'system_notification'
  
  export type UpdatePriority = 'low' | 'normal' | 'high' | 'critical'
  
  export interface WebSocketMessage {
    type: string
    channel: string
    data: RealTimeUpdate
    timestamp: DateString
  }
  
  /**
   * Analytics Query Builder Types
   */
  
  export interface AnalyticsQuery {
    id: string
    name: string
    description?: string
    data_source: string
    fields: QueryField[]
    filters: CustomFilter[]
    aggregations: QueryAggregation[]
    sorting: SortConfig[]
    limit?: number
    created_at: DateString
    created_by: string
  }
  
  export interface QueryField {
    name: string
    alias?: string
    type: FieldType
    aggregation?: AggregationType
  }
  
  export type FieldType = 
    | 'string'
    | 'number'
    | 'date'
    | 'boolean'
    | 'array'
    | 'object'
  
  export type AggregationType = 
    | 'sum'
    | 'avg'
    | 'min'
    | 'max'
    | 'count'
    | 'distinct'
    | 'median'
    | 'percentile'
  
  export interface QueryAggregation {
    field: string
    type: AggregationType
    alias: string
    parameters?: Record<string, unknown>
  }
  
  /**
   * Drill-down and Navigation Types
   */
  
  export interface DrillDownConfig {
    enabled: boolean
    levels: DrillDownLevel[]
    breadcrumbs: boolean
    back_navigation: boolean
  }
  
  export interface DrillDownLevel {
    level: number
    field: string
    label: string
    chart_type?: ChartType
    filters?: CustomFilter[]
  }
  
  export interface NavigationContext {
    current_level: number
    breadcrumbs: BreadcrumbItem[]
    can_drill_down: boolean
    can_drill_up: boolean
    available_dimensions: string[]
  }
  
  export interface BreadcrumbItem {
    label: string
    value: string
    level: number
    filters?: CustomFilter[]
  }
  
  /**
   * Comparison and Benchmarking Types
   */
  
  export interface ComparisonConfig {
    type: ComparisonType
    baseline: ComparisonBaseline
    targets: ComparisonTarget[]
    metrics: string[]
    time_periods: DateRange[]
  }
  
  export type ComparisonType = 
    | 'period_over_period'
    | 'target_vs_actual'
    | 'benchmark'
    | 'cohort'
    | 'segment'
  
  export interface ComparisonBaseline {
    type: 'previous_period' | 'same_period_last_year' | 'target' | 'benchmark'
    value?: number
    period?: DateRange
  }
  
  export interface ComparisonTarget {
    name: string
    value: number
    type: 'target' | 'benchmark' | 'competitor'
  }
  
  export interface ComparisonResult {
    metric: string
    current_value: number
    comparison_value: number
    difference: number
    percentage_change: Percentage
    trend: TrendDirection
    significance: 'significant' | 'moderate' | 'minimal'
  }
  
  /**
   * Advanced Analytics Types
   */
  
  export interface PredictiveModel {
    id: string
    name: string
    type: ModelType
    accuracy: number
    last_trained: DateString
    next_update: DateString
    predictions: Prediction[]
  }
  
  export type ModelType = 
    | 'linear_regression'
    | 'time_series'
    | 'classification'
    | 'clustering'
    | 'anomaly_detection'
  
  export interface Prediction {
    timestamp: DateString
    value: number
    confidence_interval: [number, number]
    confidence_score: number
  }
  
  export interface AnomalyDetection {
    timestamp: DateString
    metric: string
    expected_value: number
    actual_value: number
    anomaly_score: number
    severity: 'low' | 'medium' | 'high'
    explanation?: string
  }
  
  /**
   * Utility Types for Analytics
   */
  
  export type AnalyticsContextType = {
    dashboardConfig: DashboardConfig
    filterState: FilterState
    selectedTimeRange: TimeRangeFilter
    isLoading: boolean
    error: string | null
    
    // Actions
    updateFilters: (filters: Partial<FilterState>) => void
    setTimeRange: (timeRange: TimeRangeFilter) => void
    refreshData: () => Promise<void>
    exportData: (options: ExportOptions) => Promise<void>
    
    // Widget management
    addWidget: (widget: Omit<DashboardWidget, 'id'>) => void
    removeWidget: (widgetId: string) => void
    updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => void
    
    // Preferences
    updatePreferences: (preferences: Partial<DashboardPreferences>) => void
  }
  
  export interface AnalyticsHookOptions {
    auto_refresh?: boolean
    refresh_interval?: number
    cache_time?: number
    stale_time?: number
    retry_count?: number
  }
  
  export interface AnalyticsHookReturn<T> {
    data: T | null
    isLoading: boolean
    isError: boolean
    error: Error | null
    refetch: () => Promise<void>
    isRefetching: boolean
    lastUpdated: DateString | null
  }