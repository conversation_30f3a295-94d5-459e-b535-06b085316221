// src/types/auth.ts
// Authentication and user management types for KAH

import { DateString, ID, LoadingState, ApiResponse, Address } from './common'

/**
 * User roles in the KAYDAN system
 */
export type UserRole = 
  | 'admin'           // Full system access
  | 'executive'       // Strategic dashboard access
  | 'manager'         // Operational dashboard access
  | 'analyst'         // Analytical dashboard access
  | 'viewer'          // Read-only access

/**
 * KAYDAN subsidiaries
 */
export type KaydanSubsidiary = 
  | 'kaydan_groupe'
  | 'kaydan_real_estate'
  | 'artemis_construction'
  | 'ecole_des_talents'
  | 'other'

/**
 * User permissions for specific features
 */
export interface UserPermissions {
  can_view_dashboard: boolean
  can_export_data: boolean
  can_manage_projects: boolean
  can_manage_users: boolean
  can_access_analytics: boolean
  can_generate_reports: boolean
  can_manage_alerts: boolean
  can_view_financial_data: boolean
}

/**
 * Core user profile information
 */
export interface User {
  id: ID
  email: string
  first_name: string
  last_name: string
  full_name?: string
  kaydan_subsidiary: KaydanSubsidiary
  role: UserRole
  permissions: UserPermissions
  is_active: boolean
  is_staff: boolean
  date_joined: DateString
  last_login?: DateString
  profile?: UserProfile
}

/**
 * Extended user profile information
 */
export interface UserProfile {
  id: ID
  user_id: ID
  profile_picture?: string
  phone_number?: string
  country?: string
  address?: Address
  timezone?: string
  language_preference: 'en' | 'fr'
  notification_preferences: NotificationPreferences
  dashboard_preferences: DashboardPreferences
  created_at: DateString
  updated_at: DateString
}

/**
 * User notification preferences
 */
export interface NotificationPreferences {
  email_notifications: boolean
  push_notifications: boolean
  alert_notifications: boolean
  report_notifications: boolean
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly'
}

/**
 * User dashboard preferences
 */
export interface DashboardPreferences {
  default_dashboard: 'executive' | 'operational' | 'analytical'
  default_time_range: string
  favorite_widgets: string[]
  dashboard_layout: Record<string, unknown>
  theme: 'light' | 'dark' | 'auto'
}

/**
 * Authentication state
 */
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  token: string | null
  refreshToken: string | null
  tokenExpiry: DateString | null
}

/**
 * Login request payload
 */
export interface LoginRequest {
  email: string
}

/**
 * Login response from backend
 */
export interface LoginResponse {
  message: string
  email: string
  otp_sent: boolean
  expires_in: number // OTP expiry time in seconds
}

/**
 * OTP verification request payload
 */
export interface OTPVerificationRequest {
  email: string
  otp_code: string
}

/**
 * OTP verification response
 */
export interface OTPVerificationResponse {
  user: User
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: 'Bearer'
}

/**
 * Token refresh request
 */
export interface TokenRefreshRequest {
  refresh_token: string
}

/**
 * Token refresh response
 */
export interface TokenRefreshResponse {
  access_token: string
  expires_in: number
  token_type: 'Bearer'
}

/**
 * Password reset request (future feature)
 */
export interface PasswordResetRequest {
  email: string
}

/**
 * Password reset confirmation (future feature)
 */
export interface PasswordResetConfirmRequest {
  email: string
  token: string
  new_password: string
}

/**
 * User profile update request
 */
export interface UserProfileUpdateRequest {
  first_name?: string
  last_name?: string
  profile_picture?: File | string
  phone_number?: string
  country?: string
  address?: Address
  timezone?: string
  language_preference?: 'en' | 'fr'
  notification_preferences?: Partial<NotificationPreferences>
  dashboard_preferences?: Partial<DashboardPreferences>
}

/**
 * Authentication error types
 */
export type AuthErrorType = 
  | 'invalid_credentials'
  | 'invalid_otp'
  | 'expired_otp'
  | 'account_disabled'
  | 'account_not_found'
  | 'too_many_attempts'
  | 'token_expired'
  | 'token_invalid'
  | 'network_error'
  | 'server_error'
  | 'unknown_error'

/**
 * Detailed authentication error
 */
export interface AuthError {
  type: AuthErrorType
  message: string
  details?: Record<string, unknown>
  retry_after?: number // For rate limiting
}

/**
 * Session information
 */
export interface SessionInfo {
  user_id: ID
  session_id: string
  device_info: DeviceInfo
  ip_address: string
  created_at: DateString
  last_activity: DateString
  expires_at: DateString
  is_active: boolean
}

/**
 * Device information for session tracking
 */
export interface DeviceInfo {
  user_agent: string
  browser: string
  os: string
  device_type: 'mobile' | 'tablet' | 'desktop'
  is_mobile: boolean
}

/**
 * Login form state
 */
export interface LoginFormState {
  email: string
  isSubmitting: boolean
  error: string | null
}

/**
 * OTP form state
 */
export interface OTPFormState {
  otp_code: string
  isSubmitting: boolean
  error: string | null
  canResend: boolean
  resendCountdown: number
}

/**
 * Auth context type for React context
 */
export interface AuthContextType {
  // State
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: AuthError | null
  
  // Actions
  login: (email: string) => Promise<LoginResponse>
  verifyOTP: (email: string, otpCode: string) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  updateProfile: (updates: UserProfileUpdateRequest) => Promise<User>
  clearError: () => void
  
  // Utilities
  hasPermission: (permission: keyof UserPermissions) => boolean
  hasRole: (role: UserRole | UserRole[]) => boolean
  getTokenHeader: () => string | null
}

/**
 * Route protection requirements
 */
export interface RouteProtection {
  requireAuth: boolean
  requiredRoles?: UserRole[]
  requiredPermissions?: (keyof UserPermissions)[]
  redirectTo?: string
}

/**
 * JWT token payload structure
 */
export interface JWTPayload {
  user_id: ID
  email: string
  role: UserRole
  subsidiary: KaydanSubsidiary
  permissions: string[]
  iat: number // Issued at
  exp: number // Expires at
  jti: string // JWT ID
}

/**
 * Auth API endpoints configuration
 */
export interface AuthEndpoints {
  login: string
  verifyOTP: string
  refresh: string
  logout: string
  profile: string
  updateProfile: string
}

/**
 * Auth configuration
 */
export interface AuthConfig {
  endpoints: AuthEndpoints
  tokenKey: string
  refreshTokenKey: string
  tokenExpiration: number
  otpLength: number
  otpExpiration: number
  maxLoginAttempts: number
  sessionTimeout: number
}

/**
 * Wrapped auth API responses
 */
export type LoginApiResponse = ApiResponse<LoginResponse>
export type OTPVerificationApiResponse = ApiResponse<OTPVerificationResponse>
export type TokenRefreshApiResponse = ApiResponse<TokenRefreshResponse>
export type UserProfileApiResponse = ApiResponse<User>

/**
 * Auth hook return types
 */
export interface UseAuthReturn extends AuthContextType {}

export interface UseLoginReturn {
  login: (email: string) => Promise<void>
  isLoading: boolean
  error: AuthError | null
  clearError: () => void
}

export interface UseOTPReturn {
  verifyOTP: (otpCode: string) => Promise<void>
  resendOTP: () => Promise<void>
  isLoading: boolean
  error: AuthError | null
  canResend: boolean
  resendCountdown: number
  clearError: () => void
}