// src/types/common.ts
// Common and shared types used throughout the KAH application

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T = unknown> {
    status: 'success' | 'error'
    message?: string
    data?: T
    error?: string
  }
  
  /**
   * Paginated response structure
   */
  export interface PaginatedResponse<T> {
    status: 'success' | 'error'
    count: number
    next?: string | null
    previous?: string | null
    results: T[]
  }
  
  /**
   * Generic error structure
   */
  export interface ApiError {
    status: 'error'
    message: string
    details?: Record<string, string[]>
    code?: string | number
  }
  
  /**
   * Loading states for async operations
   */
  export type LoadingState = 'idle' | 'loading' | 'success' | 'error'
  
  /**
   * Status variants
   */
  export type Status = 'success' | 'warning' | 'error' | 'info'
  
  /**
   * Common ID types
   */
  export type ID = string | number
  
  /**
   * Date string format (ISO 8601)
   */
  export type DateString = string
  
  /**
   * Time range options
   */
  export interface DateRange {
    start: DateString
    end: DateString
    label?: string
  }
  
  /**
   * Predefined time range options
   */
  export type TimeRangeOption = 
    | 'today'
    | 'yesterday'
    | 'last_7_days'
    | 'last_30_days'
    | 'last_90_days'
    | 'last_6_months'
    | 'last_12_months'
    | 'year_to_date'
    | 'custom'
  
  /**
   * Sort order
   */
  export type SortOrder = 'asc' | 'desc'
  
  /**
   * Sort configuration
   */
  export interface SortConfig {
    field: string
    order: SortOrder
  }
  
  /**
   * Filter base interface
   */
  export interface BaseFilter {
    field: string
    operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'in'
    value: unknown
  }
  
  /**
   * Generic metadata interface
   */
  export interface Metadata {
    created_at: DateString
    updated_at: DateString
    created_by?: string
    updated_by?: string
  }
  
  /**
   * File upload interface
   */
  export interface FileUpload {
    file: File
    name: string
    size: number
    type: string
    lastModified: number
  }
  
  /**
   * Coordinate interface for geographical data
   */
  export interface Coordinates {
    latitude: number
    longitude: number
  }
  
  /**
   * Address interface
   */
  export interface Address {
    street?: string
    city?: string
    state?: string
    country?: string
    postal_code?: string
    coordinates?: Coordinates
  }
  
  /**
   * Currency interface
   */
  export interface Currency {
    code: string // e.g., 'USD', 'EUR', 'XOF'
    symbol: string // e.g., '$', '€', 'CFA'
    name: string // e.g., 'US Dollar', 'Euro', 'West African CFA franc'
  }
  
  /**
   * Money interface for financial data
   */
  export interface Money {
    amount: number
    currency: Currency
  }
  
  /**
   * Percentage value (0-100)
   */
  export type Percentage = number
  
  /**
   * Color values for UI components
   */
  export type Color = 
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'gray'
  
  /**
   * Size variants
   */
  export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  
  /**
   * Theme mode
   */
  export type ThemeMode = 'light' | 'dark' | 'system'
  
  /**
   * Language codes (ISO 639-1)
   */
  export type LanguageCode = 'en' | 'fr'
  
  /**
   * Locale interface
   */
  export interface Locale {
    code: LanguageCode
    name: string
    flag: string
  }
  
  /**
   * Environment types
   */
  export type Environment = 'development' | 'staging' | 'production'
  
  /**
   * Device type detection
   */
  export type DeviceType = 'mobile' | 'tablet' | 'desktop'
  
  /**
   * Responsive breakpoint
   */
  export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  
  /**
   * Generic option interface for select components
   */
  export interface Option<T = string> {
    label: string
    value: T
    disabled?: boolean
    description?: string
  }
  
  /**
   * Validation result interface
   */
  export interface ValidationResult {
    isValid: boolean
    errors: string[]
  }
  
  /**
   * Search result interface
   */
  export interface SearchResult<T> {
    item: T
    score: number
    highlights?: string[]
  }
  
  /**
   * Export format options
   */
  export type ExportFormat = 'csv' | 'xlsx' | 'pdf' | 'json'
  
  /**
   * Export configuration
   */
  export interface ExportConfig {
    format: ExportFormat
    filename?: string
    includeHeaders?: boolean
    dateRange?: DateRange
    filters?: BaseFilter[]
  }
  
  /**
   * Utility types
   */
  
  /**
   * Make specific properties required
   */
  export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>
  
  /**
   * Make specific properties optional
   */
  export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
  
  /**
   * Non-empty array type
   */
  export type NonEmptyArray<T> = [T, ...T[]]
  
  /**
   * Deep partial type
   */
  export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
  }
  
  /**
   * Deep readonly type
   */
  export type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
  }
  
  /**
   * Exact type helper
   */
  export type Exact<T, U> = T & Record<Exclude<keyof U, keyof T>, never>
  
  /**
   * String literal helper for branded types
   */
  export type Brand<T, B> = T & { __brand: B }