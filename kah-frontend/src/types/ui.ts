// src/types/ui.ts
// UI component prop types, themes, styling variants, and component interfaces

import { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes } from 'react'
import { Color, Size, LoadingState } from './common'

/**
 * Base Component Props
 */

export interface BaseComponentProps {
  className?: string
  children?: ReactNode
  id?: string
  'data-testid'?: string
}

export interface StyledComponentProps extends BaseComponentProps {
  variant?: string
  size?: Size
  color?: Color
  disabled?: boolean
}

/**
 * Theme and Styling Types
 */

export interface ThemeConfig {
  mode: ThemeMode
  colors: ColorSystem
  typography: TypographySystem
  spacing: SpacingSystem
  shadows: ShadowSystem
  borders: BorderSystem
  animations: AnimationSystem
}

export type ThemeMode = 'light' | 'dark' | 'auto' | 'system'

export interface ColorSystem {
  primary: ColorScale
  secondary: ColorScale
  success: ColorScale
  warning: ColorScale
  error: ColorScale
  info: ColorScale
  gray: ColorScale
  background: BackgroundColors
  text: TextColors
  border: BorderColors
}

export interface ColorScale {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string // Base color
  600: string
  700: string
  800: string
  900: string
}

export interface BackgroundColors {
  primary: string
  secondary: string
  surface: string
  overlay: string
  backdrop: string
}

export interface TextColors {
  primary: string
  secondary: string
  tertiary: string
  inverse: string
  disabled: string
  link: string
  link_hover: string
}

export interface BorderColors {
  default: string
  muted: string
  strong: string
  focus: string
  error: string
  success: string
}

export interface TypographySystem {
  fonts: FontFamily
  sizes: FontSizes
  weights: FontWeights
  line_heights: LineHeights
  letter_spacing: LetterSpacing
}

export interface FontFamily {
  sans: string[]
  serif: string[]
  mono: string[]
}

export interface FontSizes {
  xs: string
  sm: string
  base: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  '4xl': string
  '5xl': string
}

export interface FontWeights {
  thin: number
  light: number
  normal: number
  medium: number
  semibold: number
  bold: number
  extrabold: number
}

export interface LineHeights {
  none: number
  tight: number
  snug: number
  normal: number
  relaxed: number
  loose: number
}

export interface LetterSpacing {
  tighter: string
  tight: string
  normal: string
  wide: string
  wider: string
  widest: string
}

export interface SpacingSystem {
  0: string
  1: string
  2: string
  3: string
  4: string
  6: string
  8: string
  12: string
  16: string
  20: string
  24: string
  32: string
  40: string
  48: string
  56: string
  64: string
}

export interface ShadowSystem {
  none: string
  sm: string
  base: string
  md: string
  lg: string
  xl: string
  '2xl': string
  inner: string
}

export interface BorderSystem {
  widths: BorderWidths
  radius: BorderRadius
  styles: BorderStyles
}

export interface BorderWidths {
  0: string
  1: string
  2: string
  4: string
  8: string
}

export interface BorderRadius {
  none: string
  sm: string
  base: string
  md: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  full: string
}

export interface BorderStyles {
  solid: string
  dashed: string
  dotted: string
  double: string
  none: string
}

export interface AnimationSystem {
  durations: AnimationDurations
  easings: AnimationEasings
  transitions: TransitionPresets
}

export interface AnimationDurations {
  fast: string
  normal: string
  slow: string
  slower: string
}

export interface AnimationEasings {
  linear: string
  ease: string
  ease_in: string
  ease_out: string
  ease_in_out: string
  bounce: string
}

export interface TransitionPresets {
  fade: string
  slide: string
  scale: string
  rotate: string
  all: string
}

/**
 * Button Component Types
 */

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, StyledComponentProps {
  variant?: ButtonVariant
  size?: ButtonSize
  fullWidth?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  as?: React.ElementType
}

export type ButtonVariant = 
  | 'solid'          // Filled button
  | 'outline'        // Outlined button
  | 'ghost'          // Transparent button
  | 'link'           // Link-styled button
  | 'gradient'       // Gradient button

export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

/**
 * Input Component Types
 */

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>, StyledComponentProps {
  variant?: InputVariant
  size?: InputSize
  error?: boolean
  errorMessage?: string
  helperText?: string
  label?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  leftElement?: ReactNode
  rightElement?: ReactNode
  isRequired?: boolean
  isReadOnly?: boolean
  isInvalid?: boolean
}

export type InputVariant = 
  | 'outline'        // Outlined input
  | 'filled'         // Filled background
  | 'flushed'        // Bottom border only
  | 'unstyled'       // No styling

export type InputSize = 'sm' | 'md' | 'lg'

export interface TextareaProps extends Omit<HTMLAttributes<HTMLTextAreaElement>, 'size'>, StyledComponentProps {
  variant?: InputVariant
  size?: InputSize
  error?: boolean
  errorMessage?: string
  helperText?: string
  label?: string
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
  rows?: number
  isRequired?: boolean
  isReadOnly?: boolean
  isInvalid?: boolean
}

/**
 * Select Component Types
 */

export interface SelectOption<T = string> {
  label: string
  value: T
  disabled?: boolean
  description?: string
  icon?: ReactNode
  group?: string
}

export interface SelectProps<T = string> extends StyledComponentProps {
  options: SelectOption<T>[]
  value?: T | T[]
  defaultValue?: T | T[]
  placeholder?: string
  isMulti?: boolean
  isSearchable?: boolean
  isClearable?: boolean
  isLoading?: boolean
  isDisabled?: boolean
  isInvalid?: boolean
  error?: boolean
  errorMessage?: string
  helperText?: string
  label?: string
  size?: InputSize
  variant?: InputVariant
  onSelectionChange?: (value: T | T[] | null) => void
  onInputChange?: (value: string) => void
  renderOption?: (option: SelectOption<T>) => ReactNode
  renderValue?: (value: T) => ReactNode
}

/**
 * Card Component Types
 */

export interface CardProps extends HTMLAttributes<HTMLDivElement>, StyledComponentProps {
  variant?: CardVariant
  padding?: CardPadding
  shadow?: CardShadow
  border?: boolean
  hover?: boolean
  clickable?: boolean
  header?: ReactNode
  footer?: ReactNode
  image?: string | ReactNode
}

export type CardVariant = 
  | 'default'        // Standard card
  | 'outlined'       // Outlined card
  | 'elevated'       // Elevated with shadow
  | 'filled'         // Filled background
  | 'glass'          // Glass morphism effect

export type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl'

export type CardShadow = 'none' | 'sm' | 'md' | 'lg' | 'xl'

/**
 * Modal Component Types
 */

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  size?: ModalSize
  variant?: ModalVariant
  closeOnOverlayClick?: boolean
  closeOnEsc?: boolean
  showCloseButton?: boolean
  preventScroll?: boolean
  trapFocus?: boolean
  initialFocusRef?: React.RefObject<HTMLElement>
  finalFocusRef?: React.RefObject<HTMLElement>
  header?: ReactNode
  footer?: ReactNode
  overlayClassName?: string
}

export type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full'

export type ModalVariant = 
  | 'default'        // Standard modal
  | 'drawer'         // Side drawer
  | 'popup'          // Small popup
  | 'fullscreen'     // Fullscreen modal

/**
 * Toast/Notification Component Types
 */

export interface ToastProps extends BaseComponentProps {
  type?: ToastType
  title?: string
  description?: string
  duration?: number
  isClosable?: boolean
  position?: ToastPosition
  action?: ToastAction
  icon?: ReactNode
  onClose?: () => void
}

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading'

export type ToastPosition = 
  | 'top'
  | 'top-left'
  | 'top-right'
  | 'bottom'
  | 'bottom-left'
  | 'bottom-right'

export interface ToastAction {
  label: string
  onClick: () => void
}

/**
 * Table Component Types
 */

export interface TableProps<T = unknown> extends BaseComponentProps {
  data: T[]
  columns: TableColumn<T>[]
  variant?: TableVariant
  size?: TableSize
  striped?: boolean
  bordered?: boolean
  hoverable?: boolean
  sortable?: boolean
  selectable?: boolean
  loading?: boolean
  loadingText?: string
  emptyText?: string
  pagination?: TablePagination
  onRowClick?: (row: T, index: number) => void
  onRowSelect?: (selectedRows: T[]) => void
  onSort?: (column: string, direction: 'asc' | 'desc') => void
}

export interface TableColumn<T = unknown> {
  key: string
  header: string
  accessor?: keyof T | ((row: T) => any)
  sortable?: boolean
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
  align?: 'left' | 'center' | 'right'
  render?: (value: any, row: T, index: number) => ReactNode
  headerRender?: () => ReactNode
}

export type TableVariant = 'simple' | 'striped' | 'bordered'

export type TableSize = 'sm' | 'md' | 'lg'

export interface TablePagination {
  page: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
  onPageChange: (page: number, pageSize: number) => void
}

/**
 * Loading Component Types
 */

export interface LoadingProps extends BaseComponentProps {
  type?: LoadingType
  size?: LoadingSize
  color?: Color
  text?: string
  overlay?: boolean
  fullScreen?: boolean
}

export type LoadingType = 
  | 'spinner'        // Spinning indicator
  | 'dots'           // Bouncing dots
  | 'pulse'          // Pulsing circle
  | 'bars'           // Loading bars
  | 'skeleton'       // Skeleton placeholder

export type LoadingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

export interface SkeletonProps extends BaseComponentProps {
  width?: string | number
  height?: string | number
  count?: number
  circle?: boolean
  animation?: 'pulse' | 'wave' | 'none'
}

/**
 * Badge Component Types
 */

export interface BadgeProps extends BaseComponentProps {
  variant?: BadgeVariant
  size?: BadgeSize
  color?: Color
  dot?: boolean
  count?: number
  showZero?: boolean
  max?: number
  offset?: [number, number]
}

export type BadgeVariant = 
  | 'solid'          // Filled badge
  | 'subtle'         // Subtle background
  | 'outline'        // Outlined badge
  | 'dot'            // Dot indicator

export type BadgeSize = 'sm' | 'md' | 'lg'

/**
 * Tooltip Component Types
 */

export interface TooltipProps extends BaseComponentProps {
  content: ReactNode
  placement?: TooltipPlacement
  trigger?: TooltipTrigger
  delay?: number
  offset?: number
  arrow?: boolean
  disabled?: boolean
  maxWidth?: string | number
}

export type TooltipPlacement = 
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end'

export type TooltipTrigger = 'hover' | 'click' | 'focus' | 'manual'

/**
 * Dropdown Component Types
 */

export interface DropdownProps extends BaseComponentProps {
  trigger: ReactNode
  placement?: DropdownPlacement
  offset?: number
  closeOnSelect?: boolean
  closeOnClickOutside?: boolean
  disabled?: boolean
  isOpen?: boolean
  onOpenChange?: (isOpen: boolean) => void
}

export type DropdownPlacement = TooltipPlacement

export interface DropdownItemProps extends BaseComponentProps {
  disabled?: boolean
  destructive?: boolean
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  shortcut?: string
  onSelect?: () => void
}

/**
 * Accordion Component Types
 */

export interface AccordionProps extends BaseComponentProps {
  type?: AccordionType
  collapsible?: boolean
  defaultValue?: string | string[]
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
}

export type AccordionType = 'single' | 'multiple'

export interface AccordionItemProps extends BaseComponentProps {
  value: string
  disabled?: boolean
  header: ReactNode
  headerClassName?: string
  contentClassName?: string
}

/**
 * Tabs Component Types
 */

export interface TabsProps extends BaseComponentProps {
  defaultValue?: string
  value?: string
  orientation?: TabsOrientation
  variant?: TabsVariant
  size?: TabsSize
  onValueChange?: (value: string) => void
}

export type TabsOrientation = 'horizontal' | 'vertical'

export type TabsVariant = 
  | 'line'           // Underlined tabs
  | 'enclosed'       // Enclosed tabs
  | 'soft-rounded'   // Soft rounded tabs
  | 'solid-rounded'  // Solid rounded tabs
  | 'unstyled'       // Unstyled tabs

export type TabsSize = 'sm' | 'md' | 'lg'

export interface TabListProps extends BaseComponentProps {}

export interface TabProps extends BaseComponentProps {
  value: string
  disabled?: boolean
  leftIcon?: ReactNode
  rightIcon?: ReactNode
}

export interface TabPanelProps extends BaseComponentProps {
  value: string
}

/**
 * Alert Component Types
 */

export interface AlertProps extends BaseComponentProps {
  status?: AlertStatus
  variant?: AlertVariant
  title?: string
  description?: string
  icon?: ReactNode | boolean
  action?: ReactNode
  closable?: boolean
  onClose?: () => void
}

export type AlertStatus = 'info' | 'warning' | 'success' | 'error'

export type AlertVariant = 
  | 'subtle'         // Subtle background
  | 'left-accent'    // Left border accent
  | 'top-accent'     // Top border accent
  | 'solid'          // Solid background

/**
 * Progress Component Types
 */

export interface ProgressProps extends BaseComponentProps {
  value?: number
  max?: number
  size?: ProgressSize
  variant?: ProgressVariant
  color?: Color
  label?: string
  showValue?: boolean
  isIndeterminate?: boolean
  striped?: boolean
  animated?: boolean
}

export type ProgressSize = 'xs' | 'sm' | 'md' | 'lg'

export type ProgressVariant = 'default' | 'rounded'

/**
 * Avatar Component Types
 */

export interface AvatarProps extends BaseComponentProps {
  src?: string
  alt?: string
  name?: string
  size?: AvatarSize
  variant?: AvatarVariant
  fallback?: ReactNode
  showBorder?: boolean
  borderColor?: string
  loading?: 'eager' | 'lazy'
  onError?: () => void
}

export type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

export type AvatarVariant = 'circular' | 'rounded' | 'square'

export interface AvatarGroupProps extends BaseComponentProps {
  max?: number
  spacing?: string
  reverseOrder?: boolean
  showBorder?: boolean
  borderColor?: string
}

/**
 * Switch Component Types
 */

export interface SwitchProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>, StyledComponentProps {
  size?: SwitchSize
  variant?: SwitchVariant
  label?: string
  description?: string
  isChecked?: boolean
  defaultChecked?: boolean
  isDisabled?: boolean
  isInvalid?: boolean
  isReadOnly?: boolean
  isRequired?: boolean
  onCheckedChange?: (checked: boolean) => void
}

export type SwitchSize = 'sm' | 'md' | 'lg'

export type SwitchVariant = 'default' | 'ghost'

/**
 * Checkbox Component Types
 */

export interface CheckboxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>, StyledComponentProps {
  size?: CheckboxSize
  variant?: CheckboxVariant
  label?: string
  description?: string
  isChecked?: boolean
  defaultChecked?: boolean
  isIndeterminate?: boolean
  isDisabled?: boolean
  isInvalid?: boolean
  isReadOnly?: boolean
  isRequired?: boolean
  onCheckedChange?: (checked: boolean | 'indeterminate') => void
}

export type CheckboxSize = 'sm' | 'md' | 'lg'

export type CheckboxVariant = 'default' | 'circular'

/**
 * Radio Component Types
 */

export interface RadioProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>, StyledComponentProps {
  size?: RadioSize
  variant?: RadioVariant
  label?: string
  description?: string
  isChecked?: boolean
  isDisabled?: boolean
  isInvalid?: boolean
  isReadOnly?: boolean
  isRequired?: boolean
}

export type RadioSize = 'sm' | 'md' | 'lg'

export type RadioVariant = 'default' | 'button'

export interface RadioGroupProps extends BaseComponentProps {
  value?: string
  defaultValue?: string
  name?: string
  orientation?: 'horizontal' | 'vertical'
  size?: RadioSize
  variant?: RadioVariant
  isDisabled?: boolean
  isRequired?: boolean
  onValueChange?: (value: string) => void
}

/**
 * Form Component Types
 */

export interface FormProps extends HTMLAttributes<HTMLFormElement>, BaseComponentProps {
  onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void
}

export interface FormFieldProps extends BaseComponentProps {
  label?: string
  description?: string
  error?: string
  isRequired?: boolean
  isInvalid?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
}

export interface FormControlProps extends BaseComponentProps {
  isInvalid?: boolean
  isRequired?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
}

export interface FormLabelProps extends BaseComponentProps {
  htmlFor?: string
  isRequired?: boolean
  isInvalid?: boolean
  isDisabled?: boolean
}

export interface FormErrorMessageProps extends BaseComponentProps {}

export interface FormHelperTextProps extends BaseComponentProps {}

/**
 * Layout Component Types
 */

export interface ContainerProps extends HTMLAttributes<HTMLDivElement>, BaseComponentProps {
  maxWidth?: ContainerMaxWidth
  centerContent?: boolean
  padding?: SpacingValue
}

export type ContainerMaxWidth = 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | 'full'

export type SpacingValue = keyof SpacingSystem

export interface StackProps extends HTMLAttributes<HTMLDivElement>, BaseComponentProps {
  direction?: StackDirection
  spacing?: SpacingValue
  align?: StackAlign
  justify?: StackJustify
  wrap?: boolean
  divider?: ReactNode
}

export type StackDirection = 'row' | 'column' | 'row-reverse' | 'column-reverse'

export type StackAlign = 'start' | 'center' | 'end' | 'stretch' | 'baseline'

export type StackJustify = 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'

export interface GridProps extends HTMLAttributes<HTMLDivElement>, BaseComponentProps {
  columns?: number | ResponsiveValue<number>
  rows?: number | ResponsiveValue<number>
  gap?: SpacingValue | ResponsiveValue<SpacingValue>
  columnGap?: SpacingValue | ResponsiveValue<SpacingValue>
  rowGap?: SpacingValue | ResponsiveValue<SpacingValue>
  autoFlow?: GridAutoFlow
  autoColumns?: string
  autoRows?: string
}

export type GridAutoFlow = 'row' | 'column' | 'row-dense' | 'column-dense'

export interface ResponsiveValue<T> {
  base?: T
  sm?: T
  md?: T
  lg?: T
  xl?: T
  '2xl'?: T
}

/**
 * Utility Types
 */

export interface ComponentVariantProps {
  variant?: string
  size?: string
  color?: string
}

export interface AnimationProps {
  animate?: boolean
  duration?: number
  delay?: number
  easing?: string
}

export interface InteractionProps {
  hover?: boolean
  focus?: boolean
  active?: boolean
  disabled?: boolean
}

export interface AccessibilityProps {
  'aria-label'?: string
  'aria-labelledby'?: string
  'aria-describedby'?: string
  'aria-expanded'?: boolean
  'aria-hidden'?: boolean
  'aria-selected'?: boolean
  'aria-checked'?: boolean | 'mixed'
  'aria-pressed'?: boolean
  'aria-current'?: string | boolean
  role?: string
  tabIndex?: number
}

/**
 * Hook Types for UI Components
 */

export interface UseDisclosureReturn {
  isOpen: boolean
  onOpen: () => void
  onClose: () => void
  onToggle: () => void
}

export interface UseToastReturn {
  toast: (options: Omit<ToastProps, 'id'>) => string
  closeToast: (id: string) => void
  closeAllToasts: () => void
  updateToast: (id: string, options: Partial<ToastProps>) => void
}

export interface UseThemeReturn {
  theme: ThemeConfig
  setTheme: (theme: Partial<ThemeConfig>) => void
  toggleMode: () => void
  mode: ThemeMode
  setMode: (mode: ThemeMode) => void
}

export interface UseBreakpointReturn {
  isBase: boolean
  isSm: boolean
  isMd: boolean
  isLg: boolean
  isXl: boolean
  is2Xl: boolean
  current: string
}