// src/types/index.ts
// Main type exports for KAH (KAYDAN Analytics Hub)
// Single import point for all TypeScript types across the application

/**
 * Common Types
 * Base types used throughout the application
 */
export type {
    // API Response Types
    ApiResponse,
    PaginatedResponse,
    ApiError,
    
    // Loading and Status Types
    LoadingState,
    Status,
    
    // Data Types
    ID,
    DateString,
    DateRange,
    TimeRangeOption,
    SortOrder,
    SortConfig,
    BaseFilter,
    Metadata,
    
    // File and Location Types
    FileUpload,
    Coordinates,
    Address,
    
    // Financial Types
    Currency,
    Money,
    Percentage,
    
    // UI Types
    Color,
    Size,
    ThemeMode,
    LanguageCode,
    Locale,
    Environment,
    DeviceType,
    Breakpoint,
    
    // Utility Types
    Option,
    ValidationResult,
    SearchResult,
    ExportFormat,
    ExportConfig,
    RequireFields,
    OptionalFields,
    NonEmptyArray,
    DeepPartial,
    DeepReadonly,
    Exact,
    Brand,
  } from './common'
  
  /**
   * Authentication Types
   * User management and authentication system
   */
  export type {
    // User Types
    User,
    UserProfile,
    UserRole,
    KaydanSubsidiary,
    UserPermissions,
    NotificationPreferences,
    DashboardPreferences,
    
    // Authentication State
    AuthState,
    AuthError,
    AuthErrorType,
    SessionInfo,
    DeviceInfo,
    
    // Authentication Flow
    LoginRequest,
    LoginResponse,
    OTPVerificationRequest,
    OTPVerificationResponse,
    TokenRefreshRequest,
    TokenRefreshResponse,
    UserProfileUpdateRequest,
    
    // Form States
    LoginFormState,
    OTPFormState,
    
    // Context and Hooks
    AuthContextType,
    RouteProtection,
    JWTPayload,
    AuthEndpoints,
    AuthConfig,
    
    // API Response Wrappers
    LoginApiResponse,
    OTPVerificationApiResponse,
    TokenRefreshApiResponse,
    UserProfileApiResponse,
    
    // Hook Return Types
    UseAuthReturn,
    UseLoginReturn,
    UseOTPReturn,
  } from './auth'
  
  /**
   * API Data Types
   * Backend data models and API response structures
   */
  export type {
    // Core Data Models
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    
    // Analytics Models
    ProjectAnalytics,
    StockAnalytics,
    BusinessKPIs,
    AlertSystem,
    
    // Supporting Data Types
    GStockApprovisionnementData,
    GStockSortieData,
    GStockConsommationData,
    GStockAchatData,
    GProjetData,
    EcoleTalentsData,
    
    SupplierInfo,
    MaterialInfo,
    DeliveryInfo,
    ProjectAllocation,
    MaterialUsage,
    EfficiencyMetrics,
    ConsumptionTrend,
    PurchaseOrder,
    CostAnalysis,
    SupplierPerformance,
    
    ProjectTask,
    ProjectMilestone,
    ProjectTimeline,
    ProjectResource,
    MaterialPerformance,
    CostTrend,
    SavingsOpportunity,
    
    StudentInfo,
    CourseInfo,
    TrainingMetrics,
    
    // Enum Types
    DeliveryPerformance,
    AnalysisPeriod,
    KPIType,
    AlertType,
    AlertSeverity,
    ProjectStatus,
    
    // API Response Types
    DQEDataListResponse,
    DQEDataDetailResponse,
    GStockApprovisionnementListResponse,
    GStockSortieListResponse,
    GStockConsommationListResponse,
    GStockAchatListResponse,
    ProjectAnalyticsListResponse,
    ProjectAnalyticsDetailResponse,
    StockAnalyticsResponse,
    BusinessKPIsResponse,
    BusinessKPIsListResponse,
    AlertSystemListResponse,
    AlertSystemDetailResponse,
    AlertStatisticsResponse,
    DashboardSummaryResponse,
    AnalyticsInsightsResponse,
    PerformanceTrendsResponse,
    
    // Filter Types
    ProjectAnalyticsFilters,
    AlertFilters,
    KPIFilters,
    PaginationParams,
    AnalyticsQueryParams,
    
    // Dashboard Analytics Types
    DashboardSummary,
    AnalyticsInsight,
    PerformanceTrend,
    AlertStatistics,
    AlertPeriodStats,
    AlertTrendStats,
  } from './api'
  
  /**
   * Analytics and Dashboard Types
   * Dashboard configuration, charts, KPIs, and visualization
   */
  export type {
    // Dashboard Configuration
    DashboardType,
    DashboardConfig,
    DashboardLayout,
    ResponsiveLayout,
    DashboardWidget,
    WidgetType,
    WidgetPosition,
    WidgetSize,
    WidgetConfig,
    
    // Filter and Query Types
    FilterState,
    TimeRangeFilter,
    TimeGranularity,
    ProjectFilter,
    StatusFilter,
    CategoryFilter,
    CustomFilter,
    FilterOperator,
    FilterValue,
    
    // KPI and Metrics
    KPIMetric,
    MetricFormat,
    TrendData,
    TrendDirection,
    TrendConfidence,
    MetricThreshold,
    KPIGroup,
    
    // Chart Data Types
    ChartDataPoint,
    TimeSeriesDataPoint,
    MultiSeriesDataPoint,
    ComparisonDataPoint,
    HeatmapDataPoint,
    GeographicalDataPoint,
    
    // Chart Configuration
    ChartConfig,
    ChartType,
    ChartData,
    ChartDataset,
    ChartOptions,
    ChartStyle,
    LineStyle,
    MarkerStyle,
    AnimationConfig,
    InteractionConfig,
    LegendConfig,
    TooltipConfig,
    AxesConfig,
    AxisConfig,
    GridConfig,
    
    // Color and Themes
    ColorScheme,
    ColorPalette,
    
    // Performance Metrics
    PerformanceMetrics,
    ProjectPerformanceMetrics,
    FinancialPerformanceMetrics,
    OperationalPerformanceMetrics,
    StockPerformanceMetrics,
    
    // Alert and Notifications
    AlertSummary,
    NotificationPreference,
    NotificationMethod,
    NotificationFrequency,
    
    // Dashboard Preferences
    DashboardPreferences,
    
    // Export and Sharing
    ExportOptions,
    ShareConfig,
    SharePermissions,
    
    // Real-time Data
    RealTimeUpdate,
    UpdateType,
    UpdatePriority,
    WebSocketMessage,
    
    // Analytics Query Builder
    AnalyticsQuery,
    QueryField,
    FieldType,
    AggregationType,
    QueryAggregation,
    
    // Drill-down and Navigation
    DrillDownConfig,
    DrillDownLevel,
    NavigationContext as AnalyticsNavigationContext,
    BreadcrumbItem as AnalyticsBreadcrumbItem,
    
    // Comparison and Benchmarking
    ComparisonConfig,
    ComparisonType,
    ComparisonBaseline,
    ComparisonTarget,
    ComparisonResult,
    
    // Advanced Analytics
    PredictiveModel,
    ModelType,
    Prediction,
    AnomalyDetection,
    
    // Context and Hooks
    AnalyticsContextType,
    AnalyticsHookOptions,
    AnalyticsHookReturn,
  } from './analytics'
  
  /**
   * Navigation Types
   * Routing, navigation, breadcrumbs, and menu systems
   */
  export type {
    // Route Definition
    Route,
    RouteProtection as NavigationRouteProtection,
    RouteMetadata,
    LayoutType,
    
    // Navigation Menu
    NavMenuItem,
    MenuItemType,
    MenuBadge,
    MenuItemMetadata,
    
    // Sidebar Navigation
    SidebarConfig,
    SidebarVariant,
    SidebarWidth,
    SidebarPosition,
    SidebarBehavior,
    SidebarStyling,
    SidebarState,
    
    // Breadcrumb Navigation
    BreadcrumbItem as NavigationBreadcrumbItem,
    BreadcrumbConfig,
    BreadcrumbState,
    
    // Header Navigation
    HeaderConfig,
    HeaderVariant,
    HeaderHeight,
    HeaderPosition,
    HeaderContent,
    HeaderAction,
    HeaderStyling,
    
    // Tab Navigation
    TabConfig,
    TabState,
    
    // Mobile Navigation
    MobileNavConfig,
    MobileNavType,
    MobileNavItem,
    MobileNavBehavior,
    
    // Search Navigation
    SearchConfig,
    SearchSource,
    SearchSourceType,
    SearchShortcut,
    SearchResult as NavigationSearchResult,
    
    // Context Menu
    ContextMenuConfig,
    ContextMenuItem,
    MenuPosition,
    ContextMenuTrigger,
    
    // Navigation State
    NavigationState,
    NavigationContext,
    NavigationOptions,
    
    // Route Guards
    RouteGuard,
    GuardResult,
    RouteGuardContext,
    
    // Navigation Configuration
    NavigationConfig,
    
    // Dashboard Navigation
    DashboardNavigation,
    DashboardSection,
    DashboardNavItem,
    QuickAction,
    UserShortcut,
    
    // Navigation Analytics
    NavigationAnalytics,
    PageView,
    UserJourney,
    JourneyStep,
    PopularPath,
    ExitPoint,
    NavigationPerformance,
    
    // Hook Return Types
    UseNavigationReturn,
    UseRouteGuardsReturn,
    UseBreadcrumbsReturn,
    UseSidebarReturn,
    UseSearchReturn,
    
    // Utility Types
    NavigationEventType,
    NavigationEvent,
    RouteChangeEvent,
    NavigationErrorType,
    NavigationError,
  } from './navigation'
  
  /**
   * UI Component Types
   * Component props, themes, styling, and variants
   */
  export type {
    // Base Component Props
    BaseComponentProps,
    StyledComponentProps,
    
    // Theme System
    ThemeConfig,
    ThemeMode as UIThemeMode,
    ColorSystem,
    ColorScale,
    BackgroundColors,
    TextColors,
    BorderColors,
    TypographySystem,
    FontFamily,
    FontSizes,
    FontWeights,
    LineHeights,
    LetterSpacing,
    SpacingSystem,
    ShadowSystem,
    BorderSystem,
    BorderWidths,
    BorderRadius,
    BorderStyles,
    AnimationSystem,
    AnimationDurations,
    AnimationEasings,
    TransitionPresets,
    
    // Button Components
    ButtonProps,
    ButtonVariant,
    ButtonSize,
    
    // Input Components
    InputProps,
    InputVariant,
    InputSize,
    TextareaProps,
    
    // Select Components
    SelectOption,
    SelectProps,
    
    // Card Components
    CardProps,
    CardVariant,
    CardPadding,
    CardShadow,
    
    // Modal Components
    ModalProps,
    ModalSize,
    ModalVariant,
    
    // Toast Components
    ToastProps,
    ToastType,
    ToastPosition,
    ToastAction,
    
    // Table Components
    TableProps,
    TableColumn,
    TableVariant,
    TableSize,
    TablePagination,
    
    // Loading Components
    LoadingProps,
    LoadingType,
    LoadingSize,
    SkeletonProps,
    
    // Badge Components
    BadgeProps,
    BadgeVariant,
    BadgeSize,
    
    // Tooltip Components
    TooltipProps,
    TooltipPlacement,
    TooltipTrigger,
    
    // Dropdown Components
    DropdownProps,
    DropdownPlacement,
    DropdownItemProps,
    
    // Accordion Components
    AccordionProps,
    AccordionType,
    AccordionItemProps,
    
    // Tabs Components
    TabsProps,
    TabsOrientation,
    TabsVariant,
    TabsSize,
    TabListProps,
    TabProps,
    TabPanelProps,
    
    // Alert Components
    AlertProps,
    AlertStatus,
    AlertVariant,
    
    // Progress Components
    ProgressProps,
    ProgressSize,
    ProgressVariant,
    
    // Avatar Components
    AvatarProps,
    AvatarSize,
    AvatarVariant,
    AvatarGroupProps,
    
    // Form Components
    SwitchProps,
    SwitchSize,
    SwitchVariant,
    CheckboxProps,
    CheckboxSize,
    CheckboxVariant,
    RadioProps,
    RadioSize,
    RadioVariant,
    RadioGroupProps,
    FormProps,
    FormFieldProps,
    FormControlProps,
    FormLabelProps,
    FormErrorMessageProps,
    FormHelperTextProps,
    
    // Layout Components
    ContainerProps,
    ContainerMaxWidth,
    SpacingValue,
    StackProps,
    StackDirection,
    StackAlign,
    StackJustify,
    GridProps,
    GridAutoFlow,
    ResponsiveValue,
    
    // Utility Types
    ComponentVariantProps,
    AnimationProps,
    InteractionProps,
    AccessibilityProps,
    
    // Hook Types
    UseDisclosureReturn,
    UseToastReturn,
    UseThemeReturn,
    UseBreakpointReturn,
  } from './ui'
  
  /**
   * Re-export commonly used types for convenience
   */
  
  // Most commonly used types across the application
  export type {
    // From common
    ID,
    DateString,
    LoadingState,
    ApiResponse,
    
    // From auth
    User,
    UserRole,
    AuthState,
    
    // From analytics
    DashboardType,
    KPIMetric,
    ChartDataPoint,
    
    // From navigation
    Route,
    NavMenuItem,
    
    // From ui
    ButtonProps,
    InputProps,
    ModalProps,
    ThemeConfig as UITheme,
  }
  
  /**
   * Type guards and utility functions
   */
  
  // User role type guards
  export const isExecutiveUser = (user: User): boolean => user.role === 'executive'
  export const isManagerUser = (user: User): boolean => user.role === 'manager'
  export const isAnalystUser = (user: User): boolean => user.role === 'analyst'
  export const isAdminUser = (user: User): boolean => user.role === 'admin'
  
  // Loading state type guards
  export const isLoading = (state: LoadingState): boolean => state === 'loading'
  export const isSuccess = (state: LoadingState): boolean => state === 'success'
  export const isError = (state: LoadingState): boolean => state === 'error'
  export const isIdle = (state: LoadingState): boolean => state === 'idle'
  
  // API response type guards
  export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { status: 'success'; data: T } =>
    response.status === 'success' && response.data !== undefined
  
  export const isApiError = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { status: 'error'; error: string } =>
    response.status === 'error'
  
  // Dashboard type checks
  export const isExecutiveDashboard = (type: DashboardType): boolean => type === 'executive'
  export const isOperationalDashboard = (type: DashboardType): boolean => type === 'operational'
  export const isAnalyticalDashboard = (type: DashboardType): boolean => type === 'analytical'
  
  // Chart type checks
  export const isTimeSeriesData = (data: any): data is TimeSeriesDataPoint[] =>
    Array.isArray(data) && data.length > 0 && 'timestamp' in data[0]
  
  // Permission helpers
  export const hasPermission = (user: User, permission: keyof UserPermissions): boolean =>
    user.permissions[permission] === true
  
  export const hasRole = (user: User, roles: UserRole | UserRole[]): boolean => {
    const roleArray = Array.isArray(roles) ? roles : [roles]
    return roleArray.includes(user.role)
  }
  
  export const canAccessDashboard = (user: User, dashboardType: DashboardType): boolean => {
    switch (dashboardType) {
      case 'executive':
        return hasRole(user, ['admin', 'executive'])
      case 'operational':
        return hasRole(user, ['admin', 'executive', 'manager'])
      case 'analytical':
        return hasRole(user, ['admin', 'analyst'])
      default:
        return false
    }
  }
  
  /**
   * Type assertions and validations
   */
  
  // Validate API response structure
  export const validateApiResponse = <T>(data: unknown): data is ApiResponse<T> => {
    return (
      typeof data === 'object' &&
      data !== null &&
      'status' in data &&
      (data as any).status in ['success', 'error']
    )
  }
  
  // Validate user object
  export const validateUser = (data: unknown): data is User => {
    return (
      typeof data === 'object' &&
      data !== null &&
      'id' in data &&
      'email' in data &&
      'role' in data &&
      'permissions' in data
    )
  }
  
  // Validate chart data point
  export const validateChartDataPoint = (data: unknown): data is ChartDataPoint => {
    return (
      typeof data === 'object' &&
      data !== null &&
      'x' in data &&
      'y' in data &&
      typeof (data as any).y === 'number'
    )
  }
  
  /**
   * Constants and default values
   */
  
  // Default theme configuration
  export const DEFAULT_THEME: ThemeConfig = {
    mode: 'light',
    colors: {} as ColorSystem, // Will be populated by theme provider
    typography: {} as TypographySystem,
    spacing: {} as SpacingSystem,
    shadows: {} as ShadowSystem,
    borders: {} as BorderSystem,
    animations: {} as AnimationSystem,
  }
  
  // Default dashboard preferences
  export const DEFAULT_DASHBOARD_PREFERENCES: DashboardPreferences = {
    default_time_range: 'last_30_days',
    auto_refresh_interval: 300, // 5 minutes
    show_animations: true,
    compact_view: false,
    show_tooltips: true,
    color_scheme: 'default',
    language: 'en',
    currency: 'XOF', // West African CFA franc
    timezone: 'Africa/Abidjan',
    favorite_widgets: [],
    hidden_widgets: [],
    custom_layouts: {},
  }
  
  // Available languages
  export const SUPPORTED_LANGUAGES: Locale[] = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
  ]
  
  // Available dashboard types
  export const DASHBOARD_TYPES: DashboardType[] = ['executive', 'operational', 'analytical']
  
  // Available user roles
  export const USER_ROLES: UserRole[] = ['admin', 'executive', 'manager', 'analyst', 'viewer']
  
  /**
   * Branded types for enhanced type safety
   */
  
  // Branded ID types for different entities
  export type UserId = Brand<string, 'UserId'>
  export type ProjectId = Brand<string, 'ProjectId'>
  export type AlertId = Brand<string, 'AlertId'>
  export type WidgetId = Brand<string, 'WidgetId'>
  export type RouteId = Brand<string, 'RouteId'>
  
  // Helper functions to create branded IDs
  export const createUserId = (id: string): UserId => id as UserId
  export const createProjectId = (id: string): ProjectId => id as ProjectId
  export const createAlertId = (id: string): AlertId => id as AlertId
  export const createWidgetId = (id: string): WidgetId => id as WidgetId
  export const createRouteId = (id: string): RouteId => id as RouteId