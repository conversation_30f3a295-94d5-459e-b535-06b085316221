/* src/styles/globals.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-white font-sans text-gray-900 antialiased;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  html {
    @apply scroll-smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-2 outline-offset-2 outline-primary-500;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-lg bg-primary-500 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-outline {
    @apply inline-flex items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Input styles */
  .input-base {
    @apply block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:bg-gray-50 disabled:text-gray-500;
  }

  .input-error {
    @apply border-error-500 focus:border-error-500 focus:ring-error-500;
  }

  /* Card styles */
  .card {
    @apply rounded-xl border border-gray-200 bg-white p-6 shadow-soft;
  }

  .card-hover {
    @apply transition-shadow hover:shadow-medium;
  }

  /* Layout utilities */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }

  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-in {
    @apply animate-slide-up;
  }

  .animate-pulse-slow {
    @apply animate-pulse-slow;
  }
}

/* Utility classes */
@layer utilities {
  /* Custom utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glass effect */
  .glass {
    @apply bg-white/70 backdrop-blur-md border border-white/20;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .bg-gradient-gray {
    @apply bg-gradient-to-r from-gray-50 to-gray-100;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Dashboard grid */
  .dashboard-grid {
    @apply grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Responsive text */
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  /* Safe area for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
  /* Add dark mode styles here if needed */
}

/* Print styles */
@media print {
  .no-print {
    @apply hidden;
  }

  .print-only {
    @apply block;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}