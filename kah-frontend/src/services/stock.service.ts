// src/services/stock.service.ts
// Stock management service for KAH

import { analyticsApiClient } from './api'
import type {
  GStockApprovisionnement,
  GStockSortie,
  GStockConsommation,
  GStockAchat,
  StockAnalytics,
  MaterialItem,
  SupplierInfo,
  DeliverySchedule,
  DispatchDetail,
  ForecastedNeed,
  VendorPerformance
} from '../types/api'

/**
 * Stock service class
 */
class StockService {
  /**
   * Get stock approvisionnement data
   */
  async getStockApprovisionnement(): Promise<GStockApprovisionnement[]> {
    try {
      const response = await analyticsApiClient.get<GStockApprovisionnement[]>(
        '/gstock-approvisionnement/list_all/'
      )
      return response.data!
    } catch (error: any) {
      console.error('Get stock approvisionnement failed:', error)
      throw error
    }
  }

  /**
   * Get stock sortie data
   */
  async getStockSortie(): Promise<GStockSortie[]> {
    try {
      const response = await analyticsApiClient.get<GStockSortie[]>('/gstock-sortie/list_all/')
      return response.data!
    } catch (error: any) {
      console.error('Get stock sortie failed:', error)
      throw error
    }
  }

  /**
   * Get stock consommation data
   */
  async getStockConsommation(): Promise<GStockConsommation[]> {
    try {
      const response = await analyticsApiClient.get<GStockConsommation[]>(
        '/gstock-consommation/list_all/'
      )
      return response.data!
    } catch (error: any) {
      console.error('Get stock consommation failed:', error)
      throw error
    }
  }

  /**
   * Get stock achat data
   */
  async getStockAchat(): Promise<GStockAchat[]> {
    try {
      const response = await analyticsApiClient.get<GStockAchat[]>('/gstock-achat/list_all/')
      return response.data!
    } catch (error: any) {
      console.error('Get stock achat failed:', error)
      throw error
    }
  }

  /**
   * Get stock analytics
   */
  async getStockAnalytics(): Promise<StockAnalytics> {
    try {
      const response = await analyticsApiClient.get<StockAnalytics>('/stock-analytics/latest_analysis/')
      return response.data!
    } catch (error: any) {
      console.error('Get stock analytics failed:', error)
      throw error
    }
  }

  /**
   * Get stock efficiency trends
   */
  async getStockEfficiencyTrends(): Promise<any> {
    try {
      const response = await analyticsApiClient.get('/stock-analytics/efficiency_trends/')
      return response.data!
    } catch (error: any) {
      console.error('Get stock efficiency trends failed:', error)
      throw error
    }
  }

  /**
   * Get material items
   */
  async getMaterialItems(): Promise<MaterialItem[]> {
    try {
      const response = await analyticsApiClient.get<MaterialItem[]>('/stock/materials/')
      return response.data!
    } catch (error: any) {
      console.error('Get material items failed:', error)
      throw error
    }
  }

  /**
   * Get supplier information
   */
  async getSupplierInfo(): Promise<SupplierInfo[]> {
    try {
      const response = await analyticsApiClient.get<SupplierInfo[]>('/stock/suppliers/')
      return response.data!
    } catch (error: any) {
      console.error('Get supplier info failed:', error)
      throw error
    }
  }

  /**
   * Get delivery schedules
   */
  async getDeliverySchedules(): Promise<DeliverySchedule[]> {
    try {
      const response = await analyticsApiClient.get<DeliverySchedule[]>('/stock/deliveries/')
      return response.data!
    } catch (error: any) {
      console.error('Get delivery schedules failed:', error)
      throw error
    }
  }

  /**
   * Get dispatch details
   */
  async getDispatchDetails(): Promise<DispatchDetail[]> {
    try {
      const response = await analyticsApiClient.get<DispatchDetail[]>('/stock/dispatches/')
      return response.data!
    } catch (error: any) {
      console.error('Get dispatch details failed:', error)
      throw error
    }
  }

  /**
   * Get forecasted needs
   */
  async getForecastedNeeds(): Promise<ForecastedNeed[]> {
    try {
      const response = await analyticsApiClient.get<ForecastedNeed[]>('/stock/forecasts/')
      return response.data!
    } catch (error: any) {
      console.error('Get forecasted needs failed:', error)
      throw error
    }
  }

  /**
   * Get vendor performance
   */
  async getVendorPerformance(): Promise<VendorPerformance[]> {
    try {
      const response = await analyticsApiClient.get<VendorPerformance[]>('/stock/vendors/performance/')
      return response.data!
    } catch (error: any) {
      console.error('Get vendor performance failed:', error)
      throw error
    }
  }

  /**
   * Update material stock level
   */
  async updateStockLevel(materialId: string, quantity: number): Promise<MaterialItem> {
    try {
      const response = await analyticsApiClient.patch<MaterialItem>(`/stock/materials/${materialId}/`, {
        quantity
      })
      return response.data!
    } catch (error: any) {
      console.error('Update stock level failed:', error)
      throw error
    }
  }

  /**
   * Create new delivery schedule
   */
  async createDeliverySchedule(schedule: Omit<DeliverySchedule, 'status'>): Promise<DeliverySchedule> {
    try {
      const response = await analyticsApiClient.post<DeliverySchedule>('/stock/deliveries/', schedule)
      return response.data!
    } catch (error: any) {
      console.error('Create delivery schedule failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const stockService = new StockService()
