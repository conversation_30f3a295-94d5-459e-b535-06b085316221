// src/services/alerts.service.ts
// Alert management service for KAH

import { analyticsApiClient } from './api'
import type { ID } from '../types/common'
import type {
  AlertSystem,
  AlertStatistics,
  CreateAlertRequest,
  UpdateAlertRequest,
  AlertFilters
} from '../types/api'

/**
 * Alert service class
 */
class AlertService {
  /**
   * Get active alerts
   */
  async getActiveAlerts(): Promise<AlertSystem[]> {
    try {
      const response = await analyticsApiClient.get<AlertSystem[]>('/alerts/active_alerts/')
      return response.data!
    } catch (error: any) {
      console.error('Get active alerts failed:', error)
      throw error
    }
  }

  /**
   * Get alerts with filters
   */
  async getAlerts(filters?: AlertFilters): Promise<AlertSystem[]> {
    try {
      const response = await analyticsApiClient.get<AlertSystem[]>('/alerts/', {
        params: filters
      })
      return response.data!
    } catch (error: any) {
      console.error('Get alerts failed:', error)
      throw error
    }
  }

  /**
   * Get alert statistics
   */
  async getAlertStatistics(): Promise<AlertStatistics> {
    try {
      const response = await analyticsApiClient.get<AlertStatistics>('/alerts/alert_statistics/')
      return response.data!
    } catch (error: any) {
      console.error('Get alert statistics failed:', error)
      throw error
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: ID): Promise<AlertSystem> {
    try {
      const response = await analyticsApiClient.post<AlertSystem>(`/alerts/${alertId}/resolve/`)
      return response.data!
    } catch (error: any) {
      console.error('Resolve alert failed:', error)
      throw error
    }
  }

  /**
   * Create a new alert
   */
  async createAlert(alert: CreateAlertRequest): Promise<AlertSystem> {
    try {
      const response = await analyticsApiClient.post<AlertSystem>('/alerts/', alert)
      return response.data!
    } catch (error: any) {
      console.error('Create alert failed:', error)
      throw error
    }
  }

  /**
   * Update an existing alert
   */
  async updateAlert(alertId: ID, updates: UpdateAlertRequest): Promise<AlertSystem> {
    try {
      const response = await analyticsApiClient.patch<AlertSystem>(`/alerts/${alertId}/`, updates)
      return response.data!
    } catch (error: any) {
      console.error('Update alert failed:', error)
      throw error
    }
  }

  /**
   * Delete an alert
   */
  async deleteAlert(alertId: ID): Promise<void> {
    try {
      await analyticsApiClient.delete(`/alerts/${alertId}/`)
    } catch (error: any) {
      console.error('Delete alert failed:', error)
      throw error
    }
  }

  /**
   * Get alert history
   */
  async getAlertHistory(alertId: ID): Promise<AlertSystem[]> {
    try {
      const response = await analyticsApiClient.get<AlertSystem[]>(`/alerts/${alertId}/history/`)
      return response.data!
    } catch (error: any) {
      console.error('Get alert history failed:', error)
      throw error
    }
  }

  /**
   * Bulk resolve alerts
   */
  async bulkResolveAlerts(alertIds: ID[]): Promise<AlertSystem[]> {
    try {
      const response = await analyticsApiClient.post<AlertSystem[]>('/alerts/bulk_resolve/', {
        alert_ids: alertIds
      })
      return response.data!
    } catch (error: any) {
      console.error('Bulk resolve alerts failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const alertService = new AlertService()