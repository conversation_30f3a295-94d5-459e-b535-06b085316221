// src/services/reports.service.ts
// Report generation and management service for KAH

import { analyticsApiClient } from './api'
import type {
  ReportConfig,
  ReportResponse,
  ExportConfig,
  ReportHistory,
  ScheduledReportConfig,
  ScheduledReport
} from '../types/api'

/**
 * Report service class
 */
class ReportService {
  /**
   * Generate a new report
   */
  async generateReport(config: ReportConfig): Promise<ReportResponse> {
    try {
      const response = await analyticsApiClient.post<ReportResponse>('/reports/generate/', config)
      return response.data!
    } catch (error: any) {
      console.error('Report generation failed:', error)
      throw error
    }
  }

  /**
   * Export data in specified format
   */
  async exportData(config: ExportConfig): Promise<Blob> {
    try {
      const response = await analyticsApiClient.post('/reports/export/', config, {
        responseType: 'blob'
      })
      return response.data as Blob
    } catch (error: any) {
      console.error('Data export failed:', error)
      throw error
    }
  }

  /**
   * Get report generation history
   */
  async getReportHistory(): Promise<ReportHistory[]> {
    try {
      const response = await analyticsApiClient.get<ReportHistory[]>('/reports/history/')
      return response.data!
    } catch (error: any) {
      console.error('Get report history failed:', error)
      throw error
    }
  }

  /**
   * Schedule a recurring report
   */
  async scheduleReport(config: ScheduledReportConfig): Promise<ScheduledReport> {
    try {
      const response = await analyticsApiClient.post<ScheduledReport>('/reports/schedule/', config)
      return response.data!
    } catch (error: any) {
      console.error('Report scheduling failed:', error)
      throw error
    }
  }

  /**
   * Get scheduled reports
   */
  async getScheduledReports(): Promise<ScheduledReport[]> {
    try {
      const response = await analyticsApiClient.get<ScheduledReport[]>('/reports/scheduled/')
      return response.data!
    } catch (error: any) {
      console.error('Get scheduled reports failed:', error)
      throw error
    }
  }

  /**
   * Update scheduled report
   */
  async updateScheduledReport(
    reportId: string,
    config: Partial<ScheduledReportConfig>
  ): Promise<ScheduledReport> {
    try {
      const response = await analyticsApiClient.patch<ScheduledReport>(
        `/reports/scheduled/${reportId}/`,
        config
      )
      return response.data!
    } catch (error: any) {
      console.error('Update scheduled report failed:', error)
      throw error
    }
  }

  /**
   * Delete scheduled report
   */
  async deleteScheduledReport(reportId: string): Promise<void> {
    try {
      await analyticsApiClient.delete(`/reports/scheduled/${reportId}/`)
    } catch (error: any) {
      console.error('Delete scheduled report failed:', error)
      throw error
    }
  }

  /**
   * Get report templates
   */
  async getReportTemplates(): Promise<ReportConfig[]> {
    try {
      const response = await analyticsApiClient.get<ReportConfig[]>('/reports/templates/')
      return response.data!
    } catch (error: any) {
      console.error('Get report templates failed:', error)
      throw error
    }
  }

  /**
   * Save report template
   */
  async saveReportTemplate(config: ReportConfig): Promise<ReportConfig> {
    try {
      const response = await analyticsApiClient.post<ReportConfig>('/reports/templates/', config)
      return response.data!
    } catch (error: any) {
      console.error('Save report template failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const reportService = new ReportService()