// src/services/api.ts
// Base HTTP client configuration for KAH API communication

import axios, { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  InternalAxiosRequestConfig 
} from 'axios'
import { ApiResponse, ApiError, LoadingState } from '@/types'

// Extend InternalAxiosRequestConfig to include metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime?: number;
      [key: string]: any;
    }
  }
}

/**
 * API Client Configuration
 */
interface ApiClientConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
}

/**
 * Request interceptor configuration
 */
interface RequestInterceptorConfig {
  onRequest?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
  onRequestError?: (error: any) => Promise<any>
}

/**
 * Response interceptor configuration
 */
interface ResponseInterceptorConfig {
  onResponse?: (response: AxiosResponse) => AxiosResponse
  onResponseError?: (error: any) => Promise<any>
}

/**
 * Default API configuration
 */
const DEFAULT_CONFIG: ApiClientConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/kay_analytics',
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
}

/**
 * Create base axios instance
 */
const createAxiosInstance = (config: Partial<ApiClientConfig> = {}): AxiosInstance => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  const instance = axios.create({
    baseURL: finalConfig.baseURL,
    timeout: finalConfig.timeout,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  })

  return instance
}

/**
 * Main API client class
 */
class ApiClient {
  private instance: AxiosInstance
  private config: ApiClientConfig
  private retryCount: Map<string, number> = new Map()

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.instance = createAxiosInstance(this.config)
    this.setupInterceptors()
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor - Add auth token
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Add authentication token if available
        const token = this.getAuthToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Add request timestamp for debugging
        config.metadata = { 
          ...config.metadata, 
          startTime: Date.now() 
        }

        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
        
        return config
      },
      (error) => {
        console.error('❌ Request Error:', error)
        return Promise.reject(this.handleError(error))
      }
    )

    // Response interceptor - Handle responses and errors
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const duration = Date.now() - (response.config.metadata?.startTime || 0)
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`)
        
        return response
      },
      async (error) => {
        const duration = Date.now() - (error.config?.metadata?.startTime || 0)
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, error)
        
        return this.handleResponseError(error)
      }
    )
  }

  /**
   * Get authentication token from storage
   */
  private getAuthToken(): string | null {
    // In a real app, this would get the token from your auth store
    return localStorage.getItem('auth_token')
  }

  /**
   * Handle response errors with retry logic
   */
  private async handleResponseError(error: any): Promise<any> {
    const { config, response } = error

    // If no config, can't retry
    if (!config) {
      return Promise.reject(this.handleError(error))
    }

    const requestKey = `${config.method}-${config.url}`
    const currentRetry = this.retryCount.get(requestKey) || 0

    // Handle 401 - Unauthorized (token expired)
    if (response?.status === 401 && !config._isRetryRequest) {
      try {
        await this.refreshAuthToken()
        config._isRetryRequest = true
        return this.instance(config)
      } catch (refreshError) {
        // Refresh failed, redirect to login
        this.handleAuthError()
        return Promise.reject(this.handleError(error))
      }
    }

    // Handle retryable errors (5xx, network errors)
    if (this.shouldRetry(error) && currentRetry < this.config.retries) {
      this.retryCount.set(requestKey, currentRetry + 1)
      
      // Exponential backoff
      const delay = this.config.retryDelay * Math.pow(2, currentRetry)
      await this.delay(delay)
      
      console.log(`🔄 Retrying request (${currentRetry + 1}/${this.config.retries}): ${config.url}`)
      return this.instance(config)
    }

    // Reset retry count for this request
    this.retryCount.delete(requestKey)
    
    return Promise.reject(this.handleError(error))
  }

  /**
   * Determine if error should be retried
   */
  private shouldRetry(error: any): boolean {
    if (!error.response) {
      // Network error, retry
      return true
    }

    const status = error.response.status
    // Retry on 5xx server errors and 429 rate limiting
    return status >= 500 || status === 429
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Refresh authentication token
   */
  private async refreshAuthToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await axios.post(`${import.meta.env.VITE_AUTH_API_URL}/refresh/`, {
        refresh_token: refreshToken
      })

      const { access_token } = response.data
      localStorage.setItem('auth_token', access_token)
    } catch (error) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      throw error
    }
  }

  /**
   * Handle authentication errors
   */
  private handleAuthError(): void {
    // Clear tokens
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    
    // Redirect to login (in a real app, use your router)
    window.location.href = '/auth/login'
  }

  /**
   * Standardize error handling
   */
  private handleError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      return {
        status: 'error',
        message: data?.message || `Request failed with status ${status}`,
        details: data?.details || {},
        code: status,
      }
    } else if (error.request) {
      // Network error
      return {
        status: 'error',
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
      }
    } else {
      // Other error
      return {
        status: 'error',
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR',
      }
    }
  }

  /**
   * Generic GET request
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.get(url, config)
      return this.transformResponse<T>(response)
    } catch (error) {
      throw error // Error already handled by interceptor
    }
  }

  /**
   * Generic POST request
   */
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.post(url, data, config)
      return this.transformResponse<T>(response)
    } catch (error) {
      throw error
    }
  }

  /**
   * Generic PUT request
   */
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.put(url, data, config)
      return this.transformResponse<T>(response)
    } catch (error) {
      throw error
    }
  }

  /**
   * Generic PATCH request
   */
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.patch(url, data, config)
      return this.transformResponse<T>(response)
    } catch (error) {
      throw error
    }
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.delete(url, config)
      return this.transformResponse<T>(response)
    } catch (error) {
      throw error
    }
  }

  /**
   * Transform axios response to our ApiResponse format
   */
  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {
    // If the response already follows our ApiResponse format
    if (response.data && typeof response.data === 'object' && 'status' in response.data) {
      return response.data as ApiResponse<T>
    }

    // Otherwise, wrap the data in our format
    return {
      status: 'success',
      data: response.data as T,
    }
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile<T>(
    url: string, 
    file: File, 
    onUploadProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await this.instance.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total && onUploadProgress) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onUploadProgress(progress)
          }
        },
      })

      return this.transformResponse<T>(response)
    } catch (error) {
      throw error
    }
  }

  /**
   * Download file with progress tracking
   */
  async downloadFile(
    url: string, 
    filename?: string,
    onDownloadProgress?: (progress: number) => void
  ): Promise<Blob> {
    try {
      const response = await this.instance.get(url, {
        responseType: 'blob',
        onDownloadProgress: (progressEvent) => {
          if (progressEvent.total && onDownloadProgress) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onDownloadProgress(progress)
          }
        },
      })

      // Create download link
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)

      return blob
    } catch (error) {
      throw error
    }
  }

  /**
   * Cancel all pending requests
   */
  cancelAllRequests(): void {
    // Create new axios instance to cancel all pending requests
    this.instance = createAxiosInstance(this.config)
    this.setupInterceptors()
    this.retryCount.clear()
  }

  /**
   * Get instance for custom configurations
   */
  getInstance(): AxiosInstance {
    return this.instance
  }

  /**
   * Update base URL
   */
  setBaseURL(baseURL: string): void {
    this.config.baseURL = baseURL
    this.instance.defaults.baseURL = baseURL
  }

  /**
   * Set default timeout
   */
  setTimeout(timeout: number): void {
    this.config.timeout = timeout
    this.instance.defaults.timeout = timeout
  }
}

/**
 * Create and export the main API client instance
 */
export const apiClient = new ApiClient()

/**
 * Create specialized API clients for different services
 */
export const authApiClient = new ApiClient({
  baseURL: import.meta.env.VITE_AUTH_API_URL || 'http://localhost:8000/api/accounts',
})

export const analyticsApiClient = new ApiClient({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/kay_analytics',
})

/**
 * Export types for use in services
 */
export type { ApiClientConfig, RequestInterceptorConfig, ResponseInterceptorConfig }

/**
 * Utility functions for API calls
 */
export const createApiUrl = (endpoint: string, params?: Record<string, any>): string => {
  if (!params) return endpoint

  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })

  const queryString = searchParams.toString()
  return queryString ? `${endpoint}?${queryString}` : endpoint
}

/**
 * Request/Response logging utilities
 */
export const enableApiLogging = (): void => {
  // Already enabled in interceptors, but could be toggled
  console.log('API logging is enabled')
}

export const disableApiLogging = (): void => {
  // Could disable logging in production
  console.log('API logging is disabled')
}

/**
 * Health check utility
 */
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health/')
    return response.status === 'success'
  } catch (error) {
    console.error('API health check failed:', error)
    return false
  }
}