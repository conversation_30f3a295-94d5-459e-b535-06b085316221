// src/services/users.service.ts
// User management service for KAH

import { authApiClient } from './api'
import type {
  User,
  UserProfile,
  UserRole,
  UserPermissions,
  UserPreferences,
  UserActivity,
  UserSession,
  TokenVerificationResponse
} from '../types/api'

/**
 * User service class
 */
class UserService {
  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await authApiClient.get<User>('/users/me/')
      return response.data!
    } catch (error: any) {
      console.error('Get current user failed:', error)
      throw error
    }
  }

  /**
   * Get user profile by ID
   */
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const response = await authApiClient.get<UserProfile>(`/users/${userId}/profile/`)
      return response.data!
    } catch (error: any) {
      console.error('Get user profile failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await authApiClient.patch<UserProfile>(`/users/${userId}/profile/`, updates)
      return response.data!
    } catch (error: any) {
      console.error('Update user profile failed:', error)
      throw error
    }
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId: string): Promise<UserRole[]> {
    try {
      const response = await authApiClient.get<UserRole[]>(`/users/${userId}/roles/`)
      return response.data!
    } catch (error: any) {
      console.error('Get user roles failed:', error)
      throw error
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    try {
      const response = await authApiClient.get<UserPermissions>(`/users/${userId}/permissions/`)
      return response.data!
    } catch (error: any) {
      console.error('Get user permissions failed:', error)
      throw error
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    try {
      const response = await authApiClient.patch<UserPreferences>(
        `/users/${userId}/preferences/`,
        preferences
      )
      return response.data!
    } catch (error: any) {
      console.error('Update user preferences failed:', error)
      throw error
    }
  }

  /**
   * Get user activity history
   */
  async getUserActivity(userId: string): Promise<UserActivity[]> {
    try {
      const response = await authApiClient.get<UserActivity[]>(`/users/${userId}/activity/`)
      return response.data!
    } catch (error: any) {
      console.error('Get user activity failed:', error)
      throw error
    }
  }

  /**
   * Get active user sessions
   */
  async getActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const response = await authApiClient.get<UserSession[]>(`/users/${userId}/sessions/`)
      return response.data!
    } catch (error: any) {
      console.error('Get active sessions failed:', error)
      throw error
    }
  }

  /**
   * Terminate user session
   */
  async terminateSession(userId: string, sessionId: string): Promise<void> {
    try {
      await authApiClient.delete(`/users/${userId}/sessions/${sessionId}/`)
    } catch (error: any) {
      console.error('Terminate session failed:', error)
      throw error
    }
  }

  /**
   * Terminate all user sessions
   */
  async terminateAllSessions(userId: string): Promise<void> {
    try {
      await authApiClient.delete(`/users/${userId}/sessions/`)
    } catch (error: any) {
      console.error('Terminate all sessions failed:', error)
      throw error
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      await authApiClient.post(`/users/${userId}/change-password/`, {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error: any) {
      console.error('Change password failed:', error)
      throw error
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await authApiClient.post('/users/reset-password/', { email })
    } catch (error: any) {
      console.error('Request password reset failed:', error)
      throw error
    }
  }

  /**
   * Verify password reset token
   */
  async verifyResetToken(token: string): Promise<boolean> {
    try {
      const response = await authApiClient.post<TokenVerificationResponse>('/users/verify-reset-token/', { token })
      return response.data!.valid
    } catch (error: any) {
      console.error('Verify reset token failed:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await authApiClient.post('/users/reset-password/confirm/', {
        token,
        new_password: newPassword
      })
    } catch (error: any) {
      console.error('Reset password failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const userService = new UserService()
