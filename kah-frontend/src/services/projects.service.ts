// src/services/projects.service.ts
// Project management service for KAH

import { analyticsApiClient } from './api'
import type {
  GProjet,
  ProjectAnalytics,
  ProjectAnalyticsFilters,
  ProjectInfo,
  Milestone,
  ResourceAllocation,
  ProjectTimeline
} from '../types/api'

/**
 * Project service class
 */
class ProjectService {
  /**
   * Get all projects
   */
  async getAllProjects(): Promise<GProjet[]> {
    try {
      const response = await analyticsApiClient.get<GProjet[]>('/gprojet/list_all/')
      return response.data!
    } catch (error: any) {
      console.error('Get all projects failed:', error)
      throw error
    }
  }

  /**
   * Get project by ID
   */
  async getProjectById(projectId: string): Promise<GProjet> {
    try {
      const response = await analyticsApiClient.get<GProjet>(`/gprojet/${projectId}/`)
      return response.data!
    } catch (error: any) {
      console.error('Get project by ID failed:', error)
      throw error
    }
  }

  /**
   * Get project analytics
   */
  async getProjectAnalytics(filters?: ProjectAnalyticsFilters): Promise<ProjectAnalytics[]> {
    try {
      const response = await analyticsApiClient.get<ProjectAnalytics[]>('/project-analytics/', {
        params: filters
      })
      return response.data!
    } catch (error: any) {
      console.error('Get project analytics failed:', error)
      throw error
    }
  }

  /**
   * Get project milestones
   */
  async getProjectMilestones(projectId: string): Promise<Milestone[]> {
    try {
      const response = await analyticsApiClient.get<Milestone[]>(`/gprojet/${projectId}/milestones/`)
      return response.data!
    } catch (error: any) {
      console.error('Get project milestones failed:', error)
      throw error
    }
  }

  /**
   * Get project resource allocation
   */
  async getProjectResources(projectId: string): Promise<ResourceAllocation[]> {
    try {
      const response = await analyticsApiClient.get<ResourceAllocation[]>(`/gprojet/${projectId}/resources/`)
      return response.data!
    } catch (error: any) {
      console.error('Get project resources failed:', error)
      throw error
    }
  }

  /**
   * Get project timeline
   */
  async getProjectTimeline(projectId: string): Promise<ProjectTimeline[]> {
    try {
      const response = await analyticsApiClient.get<ProjectTimeline[]>(`/gprojet/${projectId}/timeline/`)
      return response.data!
    } catch (error: any) {
      console.error('Get project timeline failed:', error)
      throw error
    }
  }

  /**
   * Update project status
   */
  async updateProjectStatus(projectId: string, status: ProjectInfo['status']): Promise<GProjet> {
    try {
      const response = await analyticsApiClient.patch<GProjet>(`/gprojet/${projectId}/`, {
        status
      })
      return response.data!
    } catch (error: any) {
      console.error('Update project status failed:', error)
      throw error
    }
  }

  /**
   * Update project milestone
   */
  async updateMilestone(
    projectId: string,
    milestoneId: string,
    data: Partial<Milestone>
  ): Promise<Milestone> {
    try {
      const response = await analyticsApiClient.patch<Milestone>(
        `/gprojet/${projectId}/milestones/${milestoneId}/`,
        data
      )
      return response.data!
    } catch (error: any) {
      console.error('Update milestone failed:', error)
      throw error
    }
  }

  /**
   * Update resource allocation
   */
  async updateResourceAllocation(
    projectId: string,
    resourceId: string,
    data: Partial<ResourceAllocation>
  ): Promise<ResourceAllocation> {
    try {
      const response = await analyticsApiClient.patch<ResourceAllocation>(
        `/gprojet/${projectId}/resources/${resourceId}/`,
        data
      )
      return response.data!
    } catch (error: any) {
      console.error('Update resource allocation failed:', error)
      throw error
    }
  }

  /**
   * Get project performance metrics
   */
  async getProjectPerformance(projectId: string): Promise<ProjectAnalytics> {
    try {
      const response = await analyticsApiClient.get<ProjectAnalytics>(
        `/project-analytics/${projectId}/performance/`
      )
      return response.data!
    } catch (error: any) {
      console.error('Get project performance failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const projectService = new ProjectService()
