// src/services/analytics.service.ts
// Analytics service for KAH data analysis and visualization

import {
  // Core Data Types
  DQEData,
  GStockApprovisionnement,
  GStockSortie,
  GStockConsommation,
  GStockAchat,
  GProjet,
  EcoleTalents,
  
  // Analytics Types
  ProjectAnalytics,
  StockAnalytics,
  BusinessKPIs,
  AlertSystem,
  DashboardSummary,
  AnalyticsInsight,
  PerformanceTrend,
  AlertStatistics,
  
  // Filter Types
  ProjectAnalyticsFilters,
  AlertFilters,
  KPIFilters,
  AnalyticsQueryParams,
  
  // Response Types
  DQEDataListResponse,
  ProjectAnalyticsListResponse,
  StockAnalyticsResponse,
  BusinessKPIsResponse,
  AlertSystemListResponse,
  DashboardSummaryResponse,
  AnalyticsInsightsResponse,
  PerformanceTrendsResponse,
  
  // Common Types
  DateString,
  ExportConfig,
  ApiResponse,
} from '@/types'

import { analyticsApiClient, createApiUrl } from './api'

/**
 * Analytics service configuration
 */
interface AnalyticsServiceConfig {
  enableCaching: boolean
  cacheTimeout: number
  maxRetries: number
  batchSize: number
}

/**
 * Default configuration
 */
const DEFAULT_CONFIG: AnalyticsServiceConfig = {
  enableCaching: true,
  cacheTimeout: 300000, // 5 minutes
  maxRetries: 3,
  batchSize: 100,
}

/**
 * Analytics service class
 */
class AnalyticsService {
  private config: AnalyticsServiceConfig
  private cache: Map<string, { data: any; timestamp: number }> = new Map()

  constructor(config: Partial<AnalyticsServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * Core Analytics Operations
   */

  /**
   * Run complete analytics analysis
   */
  async runAnalysis(): Promise<any> {
    try {
      const response = await analyticsApiClient.post('/analytics/run_analysis/')
      
      if (response.status === 'error') {
        throw new Error(response.message || 'Analytics analysis failed')
      }

      // Clear cache after running new analysis
      this.clearCache()

      return response.data
    } catch (error: any) {
      console.error('Analytics analysis failed:', error)
      throw error
    }
  }

  /**
   * Get dashboard summary data
   */
  async getDashboardSummary(): Promise<DashboardSummary> {
    const cacheKey = 'dashboard_summary'
    
    // Check cache first
    const cached = this.getFromCache<DashboardSummary>(cacheKey)
    if (cached) return cached

    try {
      const response = await analyticsApiClient.get<DashboardSummary>(
        '/analytics/dashboard_summary/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get dashboard summary')
      }

      const data = response.data!
      this.setCache(cacheKey, data)

      return data
    } catch (error: any) {
      console.error('Get dashboard summary failed:', error)
      throw error
    }
  }

  /**
   * Get analytics insights
   */
  async getInsights(): Promise<AnalyticsInsight[]> {
    try {
      const response = await analyticsApiClient.get<AnalyticsInsight[]>(
        '/analytics/insights/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get insights')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get insights failed:', error)
      throw error
    }
  }

  /**
   * Get performance trends
   */
  async getPerformanceTrends(): Promise<PerformanceTrend[]> {
    try {
      const response = await analyticsApiClient.get<PerformanceTrend[]>(
        '/analytics/performance_trends/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get performance trends')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get performance trends failed:', error)
      throw error
    }
  }

  /**
   * DQE Data Operations
   */

  /**
   * Get all DQE data
   */
  async getDQEData(): Promise<DQEData[]> {
    try {
      const response = await analyticsApiClient.get<DQEData[]>('/dqe/list_all/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get DQE data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get DQE data failed:', error)
      throw error
    }
  }

  /**
   * Get latest DQE data
   */
  async getLatestDQE(): Promise<DQEData> {
    try {
      const response = await analyticsApiClient.get<DQEData>('/dqe/get_latest/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get latest DQE data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get latest DQE failed:', error)
      throw error
    }
  }

  /**
   * Fetch fresh DQE data from external API
   */
  async fetchDQEData(): Promise<DQEData> {
    try {
      const response = await analyticsApiClient.get<DQEData>('/dqe/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to fetch DQE data')
      }

      // Clear DQE cache
      this.clearCacheByPattern('dqe_')

      return response.data!
    } catch (error: any) {
      console.error('Fetch DQE data failed:', error)
      throw error
    }
  }

  /**
   * G-Stock Operations
   */

  /**
   * Get G-Stock Approvisionnement data
   */
  async getStockApprovisionnement(): Promise<GStockApprovisionnement[]> {
    try {
      const response = await analyticsApiClient.get<GStockApprovisionnement[]>(
        '/gstock-approvisionnement/list_all/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock approvisionnement data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock approvisionnement failed:', error)
      throw error
    }
  }

  /**
   * Get latest G-Stock Approvisionnement
   */
  async getLatestStockApprovisionnement(): Promise<GStockApprovisionnement> {
    try {
      const response = await analyticsApiClient.get<GStockApprovisionnement>(
        '/gstock-approvisionnement/get_latest/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get latest stock approvisionnement')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get latest stock approvisionnement failed:', error)
      throw error
    }
  }

  /**
   * Fetch fresh G-Stock Approvisionnement data
   */
  async fetchStockApprovisionnement(): Promise<GStockApprovisionnement> {
    try {
      const response = await analyticsApiClient.get<GStockApprovisionnement>(
        '/gstock-approvisionnement/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to fetch stock approvisionnement')
      }

      return response.data!
    } catch (error: any) {
      console.error('Fetch stock approvisionnement failed:', error)
      throw error
    }
  }

  /**
   * Get G-Stock Sortie data
   */
  async getStockSortie(): Promise<GStockSortie[]> {
    try {
      const response = await analyticsApiClient.get<GStockSortie[]>(
        '/gstock-sortie/list_all/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock sortie data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock sortie failed:', error)
      throw error
    }
  }

  /**
   * Get G-Stock Consommation data
   */
  async getStockConsommation(): Promise<GStockConsommation[]> {
    try {
      const response = await analyticsApiClient.get<GStockConsommation[]>(
        '/gstock-consommation/list_all/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock consommation data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock consommation failed:', error)
      throw error
    }
  }

  /**
   * Get G-Stock Achat data
   */
  async getStockAchat(): Promise<GStockAchat[]> {
    try {
      const response = await analyticsApiClient.get<GStockAchat[]>(
        '/gstock-achat/list_all/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock achat data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock achat failed:', error)
      throw error
    }
  }

  /**
   * Project Analytics Operations
   */

  /**
   * Get project analytics with filters
   */
  async getProjectAnalytics(filters?: ProjectAnalyticsFilters): Promise<ProjectAnalytics[]> {
    try {
      const url = createApiUrl('/project-analytics/', filters)
      const response = await analyticsApiClient.get<ProjectAnalytics[]>(url)

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get project analytics')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get project analytics failed:', error)
      throw error
    }
  }

  /**
   * Get project summary statistics
   */
  async getProjectSummaryStats(): Promise<any> {
    try {
      const response = await analyticsApiClient.get('/project-analytics/summary_stats/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get project summary stats')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get project summary stats failed:', error)
      throw error
    }
  }

  /**
   * Stock Analytics Operations
   */

  /**
   * Get stock analytics
   */
  async getStockAnalytics(): Promise<StockAnalytics> {
    try {
      const response = await analyticsApiClient.get<StockAnalytics>(
        '/stock-analytics/latest_analysis/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock analytics')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock analytics failed:', error)
      throw error
    }
  }

  /**
   * Get stock efficiency trends
   */
  async getStockEfficiencyTrends(): Promise<any> {
    try {
      const response = await analyticsApiClient.get('/stock-analytics/efficiency_trends/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get stock efficiency trends')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get stock efficiency trends failed:', error)
      throw error
    }
  }

  /**
   * Business KPIs Operations
   */

  /**
   * Get latest business KPIs
   */
  async getLatestKPIs(): Promise<BusinessKPIs> {
    try {
      const response = await analyticsApiClient.get<BusinessKPIs>(
        '/business-kpis/latest_kpis/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get latest KPIs')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get latest KPIs failed:', error)
      throw error
    }
  }

  /**
   * Get KPI comparison data
   */
  async getKPIComparison(type: string = 'monthly'): Promise<BusinessKPIs[]> {
    try {
      const url = createApiUrl('/business-kpis/kpi_comparison/', { type })
      const response = await analyticsApiClient.get<BusinessKPIs[]>(url)

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get KPI comparison')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get KPI comparison failed:', error)
      throw error
    }
  }

  /**
   * Alert System Operations
   */

  /**
   * Get active alerts
   */
  async getActiveAlerts(): Promise<AlertSystem[]> {
    try {
      const response = await analyticsApiClient.get<AlertSystem[]>(
        '/alerts/active_alerts/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get active alerts')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get active alerts failed:', error)
      throw error
    }
  }

  /**
   * Get alerts with filters
   */
  async getAlerts(filters?: AlertFilters): Promise<AlertSystem[]> {
    try {
      const url = createApiUrl('/alerts/', filters)
      const response = await analyticsApiClient.get<AlertSystem[]>(url)

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get alerts')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get alerts failed:', error)
      throw error
    }
  }

  /**
   * Get alert statistics
   */
  async getAlertStatistics(): Promise<AlertStatistics> {
    try {
      const response = await analyticsApiClient.get<AlertStatistics>(
        '/alerts/alert_statistics/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get alert statistics')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get alert statistics failed:', error)
      throw error
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string): Promise<AlertSystem> {
    try {
      const response = await analyticsApiClient.post<AlertSystem>(
        `/alerts/${alertId}/resolve/`
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to resolve alert')
      }

      return response.data!
    } catch (error: any) {
      console.error('Resolve alert failed:', error)
      throw error
    }
  }

  /**
   * G-Projet Operations
   */

  /**
   * Get G-Projet data
   */
  async getGProjetData(): Promise<GProjet[]> {
    try {
      const response = await analyticsApiClient.get<GProjet[]>('/gprojet/list_all/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get G-Projet data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get G-Projet data failed:', error)
      throw error
    }
  }

  /**
   * Get latest G-Projet data
   */
  async getLatestGProjet(): Promise<GProjet> {
    try {
      const response = await analyticsApiClient.get<GProjet>('/gprojet/get_latest/')

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get latest G-Projet data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get latest G-Projet failed:', error)
      throw error
    }
  }

  /**
   * Ecole des Talents Operations
   */

  /**
   * Get Ecole des Talents data
   */
  async getEcoleTalentsData(): Promise<EcoleTalents[]> {
    try {
      const response = await analyticsApiClient.get<EcoleTalents[]>(
        '/ecole-talent/list_all/'
      )

      if (response.status === 'error') {
        throw new Error(response.message || 'Failed to get Ecole des Talents data')
      }

      return response.data!
    } catch (error: any) {
      console.error('Get Ecole des Talents data failed:', error)
      throw error
    }
  }

  /**
   * Data Export Operations
   */

  /**
   * Export analytics data
   */
  async exportData(config: ExportConfig): Promise<Blob> {
    try {
      const response = await analyticsApiClient.post('/analytics/export/', config, {
        responseType: 'blob'
      })

      return response.data as Blob
    } catch (error: any) {
      console.error('Export data failed:', error)
      throw error
    }
  }

  /**
   * Cache Management Methods
   */
  private getFromCache<T>(key: string): T | null {
    if (!this.config.enableCaching) return null

    const cached = this.cache.get(key)
    if (!cached) return null

    const now = Date.now()
    if (now - cached.timestamp > this.config.cacheTimeout) {
      this.cache.delete(key)
      return null
    }

    return cached.data as T
  }

  private setCache<T>(key: string, data: T): void {
    if (!this.config.enableCaching) return

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  private clearCache(): void {
    this.cache.clear()
  }

  private clearCacheByPattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(pattern)) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * Fetch all data sources in parallel
   */
  async fetchAllData(): Promise<{
    dqe: DQEData[]
    stockApprovisionnement: GStockApprovisionnement[]
    stockSortie: GStockSortie[]
    stockConsommation: GStockConsommation[]
    stockAchat: GStockAchat[]
    projectAnalytics: ProjectAnalytics[]
    stockAnalytics: StockAnalytics
    businessKPIs: BusinessKPIs
    alerts: AlertSystem[]
  }> {
    try {
      const [
        dqe,
        stockApprovisionnement,
        stockSortie,
        stockConsommation,
        stockAchat,
        projectAnalytics,
        stockAnalytics,
        businessKPIs,
        alerts
      ] = await Promise.all([
        this.getDQEData().catch(() => []),
        this.getStockApprovisionnement().catch(() => []),
        this.getStockSortie().catch(() => []),
        this.getStockConsommation().catch(() => []),
        this.getStockAchat().catch(() => []),
        this.getProjectAnalytics().catch(() => []),
        this.getStockAnalytics().catch(() => ({} as StockAnalytics)),
        this.getLatestKPIs().catch(() => ({} as BusinessKPIs)),
        this.getActiveAlerts().catch(() => [])
      ])

      return {
        dqe,
        stockApprovisionnement,
        stockSortie,
        stockConsommation,
        stockAchat,
        projectAnalytics,
        stockAnalytics,
        businessKPIs,
        alerts
      }
    } catch (error: any) {
      console.error('Fetch all data failed:', error)
      throw error
    }
  }
}

// Export a singleton instance
export const analyticsService = new AnalyticsService()
  