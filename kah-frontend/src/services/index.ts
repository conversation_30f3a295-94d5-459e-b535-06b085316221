// src/services/index.ts
// Export all services for centralized access

export { analyticsService } from './analytics.service'
export { authService } from './auth.service'
export { apiClient, authApiClient, analyticsApiClient } from './api'
export { createApiUrl, checkApiHealth } from './api'

// Re-export types
export type { ApiClientConfig, RequestInterceptorConfig, ResponseInterceptorConfig } from './api'
