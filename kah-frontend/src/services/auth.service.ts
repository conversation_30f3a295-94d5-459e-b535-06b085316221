// src/services/auth.service.ts
// Authentication service for KAH

import { authApiClient } from './api'
import type {
  LoginResponse,
  OTPVerificationResponse,
  TokenRefreshResponse,
  User,
  UserProfileUpdateRequest
} from '@/types'

/**
 * Authentication service class
 */
class AuthService {
  /**
   * Request OTP for login
   */
  async requestOTP(email: string): Promise<LoginResponse> {
    try {
      const response = await authApiClient.post<LoginResponse>('/login/', { email })
      return response.data!
    } catch (error: any) {
      console.error('OTP request failed:', error)
      throw error
    }
  }

  /**
   * Verify OTP code
   */
  async verifyOTP(email: string, code: string): Promise<OTPVerificationResponse> {
    try {
      const response = await authApiClient.post<OTPVerificationResponse>('/verify-otp/', {
        email,
        code
      })
      
      // Store tokens on successful verification
      if (response.data?.access_token) {
        localStorage.setItem('auth_token', response.data.access_token)
        if (response.data.refresh_token) {
          localStorage.setItem('refresh_token', response.data.refresh_token)
        }
      }

      return response.data!
    } catch (error: any) {
      console.error('OTP verification failed:', error)
      throw error
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<TokenRefreshResponse> {
    try {
      const response = await authApiClient.post<TokenRefreshResponse>('/refresh/', {
        refresh_token: refreshToken
      })

      if (response.data?.access_token) {
        localStorage.setItem('auth_token', response.data.access_token)
      }

      return response.data!
    } catch (error: any) {
      console.error('Token refresh failed:', error)
      throw error
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await authApiClient.post('/logout/')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await authApiClient.get<User>('/me/')
      return response.data!
    } catch (error: any) {
      console.error('Get current user failed:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(data: UserProfileUpdateRequest): Promise<User> {
    try {
      const response = await authApiClient.patch<User>('/me/', data)
      return response.data!
    } catch (error: any) {
      console.error('Update profile failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const authService = new AuthService()