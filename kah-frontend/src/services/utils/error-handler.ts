// src/services/utils/error-handler.ts
// Error handling utility for KAH services

import type { ApiError } from '../../types/api'
import { ErrorType } from '../../types/api'

/**
 * Error handler class
 */
class ErrorHandler {
  /**
   * Handle API errors
   */
  handleApiError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      return {
        status: 'error',
        message: data?.message || `Request failed with status ${status}`,
        details: data?.details || {},
        code: status,
        type: this.getErrorType(status)
      }
    } else if (error.request) {
      // Network error
      return {
        status: 'error',
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
        type: ErrorType.NETWORK
      }
    } else {
      // Other error
      return {
        status: 'error',
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR',
        type: ErrorType.UNKNOWN
      }
    }
  }

  /**
   * Get error type from status code
   */
  private getErrorType(status: number): ErrorType {
    if (status >= 500) {
      return ErrorType.SERVER
    } else if (status === 401 || status === 403) {
      return ErrorType.AUTH
    } else if (status === 400 || status === 422) {
      return ErrorType.VALIDATION
    } else {
      return ErrorType.UNKNOWN
    }
  }

  /**
   * Format error message for display
   */
  formatErrorMessage(error: ApiError): string {
    const { message, details } = error

    if (details && Object.keys(details).length > 0) {
      const detailMessages = Object.entries(details)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n')
      return `${message}\n${detailMessages}`
    }

    return message
  }

  /**
   * Check if error is retryable
   */
  isRetryable(error: ApiError): boolean {
    return (
      error.type === ErrorType.NETWORK ||
      error.type === ErrorType.SERVER ||
      error.code === 429 // Rate limit
    )
  }

  /**
   * Log error to console with appropriate level
   */
  logError(error: ApiError): void {
    const message = this.formatErrorMessage(error)

    switch (error.type) {
      case ErrorType.NETWORK:
        console.warn('Network Error:', message)
        break
      case ErrorType.AUTH:
        console.warn('Authentication Error:', message)
        break
      case ErrorType.VALIDATION:
        console.warn('Validation Error:', message)
        break
      case ErrorType.SERVER:
        console.error('Server Error:', message)
        break
      default:
        console.error('Unknown Error:', message)
    }
  }
}

// Export singleton instance
export const errorHandler = new ErrorHandler()
