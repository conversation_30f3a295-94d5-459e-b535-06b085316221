// src/services/utils/cache.ts
// Cache utility for KAH services

interface CacheConfig {
  ttl: number // Time to live in milliseconds
  maxSize: number // Maximum number of items in cache
}

interface CacheEntry<T> {
  data: T
  timestamp: number
  expiresAt: number
}

/**
 * Cache utility class
 */
class Cache {
  private cache: Map<string, CacheEntry<any>>
  private config: CacheConfig

  constructor(config: Partial<CacheConfig> = {}) {
    this.cache = new Map()
    this.config = {
      ttl: 5 * 60 * 1000, // 5 minutes default TTL
      maxSize: 1000, // 1000 items default max size
      ...config
    }
  }

  /**
   * Get item from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) return null

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  /**
   * Set item in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Check cache size and remove oldest entries if needed
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest()
    }

    const timestamp = Date.now()
    const expiresAt = timestamp + (ttl || this.config.ttl)

    this.cache.set(key, {
      data,
      timestamp,
      expiresAt
    })
  }

  /**
   * Remove item from cache
   */
  delete(key: string): void {
    this.cache.delete(key)
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitCount: number
    missCount: number
  } {
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount
    }
  }

  /**
   * Evict oldest entries when cache is full
   */
  private evictOldest(): void {
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    // Remove 10% of oldest entries
    const entriesToRemove = Math.ceil(this.config.maxSize * 0.1)
    entries.slice(0, entriesToRemove).forEach(([key]) => {
      this.cache.delete(key)
    })
  }

  // Cache statistics
  private hitCount = 0
  private missCount = 0

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.hitCount = 0
    this.missCount = 0
  }
}

// Export singleton instance
export const cache = new Cache()

// Export types
export type { CacheConfig, CacheEntry }
