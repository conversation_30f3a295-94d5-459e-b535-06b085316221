// src/services/utils/interceptors.ts
// Request/Response interceptors for KAH services

import type { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { errorHandler } from './error-handler'
import { cache } from './cache'

interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  onRequest?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
}

/**
 * Request interceptor configuration
 */
interface RequestInterceptorConfig {
  onRequest?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
  onRequestError?: (error: any) => Promise<any>
}

/**
 * Response interceptor configuration
 */
interface ResponseInterceptorConfig {
  onResponse?: (response: AxiosResponse) => AxiosResponse
  onResponseError?: (error: any) => Promise<any>
}

/**
 * Interceptors utility class
 */
class Interceptors {
  /**
   * Create request interceptor
   */
  createRequestInterceptor(config: RequestInterceptorConfig = {}) {
    return {
      onFulfilled: (config: ExtendedAxiosRequestConfig) => {
        // Add request timestamp for debugging
        config.metadata = {
          ...config.metadata,
          startTime: Date.now()
        }

        // Add authentication token if available
        const token = this.getAuthToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Log request
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)

        // Apply custom request interceptor if provided
        return config.onRequest ? config.onRequest(config) : config
      },
      onRejected: (error: any) => {
        console.error('❌ Request Error:', error)
        return config.onRequestError ? config.onRequestError(error) : Promise.reject(error)
      }
    }
  }

  /**
   * Create response interceptor
   */
  createResponseInterceptor(config: ResponseInterceptorConfig = {}) {
    return {
      onFulfilled: (response: AxiosResponse) => {
        const duration = Date.now() - (response.config.metadata?.startTime || 0)
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`)

        // Cache successful GET responses
        if (response.config.method?.toLowerCase() === 'get') {
          const cacheKey = this.generateCacheKey(response.config)
          if (cacheKey) {
            cache.set(cacheKey, response.data)
          }
        }

        // Apply custom response interceptor if provided
        return config.onResponse ? config.onResponse(response) : response
      },
      onRejected: async (error: any) => {
        const duration = Date.now() - (error.config?.metadata?.startTime || 0)
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, error)

        // Handle error with error handler
        const apiError = errorHandler.handleApiError(error)
        errorHandler.logError(apiError)

        // Apply custom error interceptor if provided
        return config.onResponseError ? config.onResponseError(error) : Promise.reject(apiError)
      }
    }
  }

  /**
   * Get authentication token from storage
   */
  private getAuthToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  /**
   * Generate cache key from request config
   */
  private generateCacheKey(config: AxiosRequestConfig): string | null {
    if (!config.url) return null

    const { url, params, data } = config
    const key = [url]

    if (params) {
      key.push(JSON.stringify(params))
    }

    if (data) {
      key.push(JSON.stringify(data))
    }

    return key.join('|')
  }
}

// Export singleton instance
export const interceptors = new Interceptors()

// Export types
export type { RequestInterceptorConfig, ResponseInterceptorConfig }
